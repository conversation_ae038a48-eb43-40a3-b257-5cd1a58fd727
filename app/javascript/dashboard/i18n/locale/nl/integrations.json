{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integraties", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Subscribed Events", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "DESC": "Webhook events bieden je realtime informatie over wat er gebeurt in je Chatwoot account. Voer een geldige URL in om een callback te configureren.", "SUBSCRIPTIONS": {"LABEL": "Gebeurtenissen", "EVENTS": {"CONVERSATION_CREATED": "Gesprek a<PERSON>ema<PERSON>", "CONVERSATION_STATUS_CHANGED": "Gespreksstatus veranderd", "CONVERSATION_UPDATED": "Gesprek bijgewerkt", "MESSAGE_CREATED": "Bericht aangemaakt", "MESSAGE_UPDATED": "Bericht bijgewerkt", "WEBWIDGET_TRIGGERED": "Live chat widget geopend door de gebruiker", "CONTACT_CREATED": "Contact aangemaakt", "CONTACT_UPDATED": "Contact aangemaakt", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "<PERSON><PERSON><PERSON> een geldige URL in"}, "EDIT_SUBMIT": "Webhook bijwerken", "ADD_SUBMIT": "<PERSON><PERSON> webhook"}, "TITLE": "Webhook", "CONFIGURE": "Configureren", "HEADER": "Webhook instellingen", "HEADER_BTN_TXT": "Nieuwe webhook toevoegen", "LOADING": "Bijgevoegde webhooks ophalen", "SEARCH_404": "Er zijn geen items die overeenkomen met deze z<PERSON>cht", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks zijn HTTP callbacks die voor elk account kunnen worden gedefinieerd. Ze worden geactiveerd door gebeurtenissen zoals het aanmaken van berichten in Chatwoot. Je kunt meer dan één webhook aanmaken voor dit account. <br /><br /> Voor het maken van een <b>webhook</b>, klik op de <b>Voeg nieuwe webhook</b> knop toe. U kunt ook alle bestaande webhook verwijderen door te klikken op de verwijderknop.</p>", "LIST": {"404": "Er zijn geen webhooks geconfigureerd voor dit account.", "TITLE": "Webhooks beheren", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook eindpunt", "ACTIONS": "Acties"}}, "EDIT": {"BUTTON_TEXT": "Bewerken", "TITLE": "Webhook bewerken", "API": {"SUCCESS_MESSAGE": "Webhook configuratie succesvol bijgewerkt", "ERROR_MESSAGE": "Kan geen verbinding maken met <PERSON><PERSON> <PERSON>, probeer het later opnieuw"}}, "ADD": {"CANCEL": "annuleren", "TITLE": "Nieuwe webhook toevoegen", "API": {"SUCCESS_MESSAGE": "Webhook configuratie succesvol toegevoegd", "ERROR_MESSAGE": "Kan geen verbinding maken met <PERSON><PERSON> <PERSON>, probeer het later opnieuw"}}, "DELETE": {"BUTTON_TEXT": "Verwijderen", "API": {"SUCCESS_MESSAGE": "Webhook succesvol verwijderd", "ERROR_MESSAGE": "Kan geen verbinding maken met <PERSON><PERSON> <PERSON>, probeer het later opnieuw"}, "CONFIRM": {"TITLE": "Verwijdering bevestigen", "MESSAGE": "Weet je zeker dat je de webhook wilt verwijderen? ({webhookURL})", "YES": "Ja, verwijderen ", "NO": "<PERSON><PERSON>, Be<PERSON>ar het"}}}, "SLACK": {"DELETE": "Verwijderen", "DELETE_CONFIRMATION": {"TITLE": "Integratie verwijderen", "MESSAGE": "Weet u zeker dat u de integratie wilt verwijderen? Dit zal leiden tot verlies van toegang tot gesprekken op uw Slack werkruimte."}, "HELP_TEXT": {"TITLE": "Using Slack Integration", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "gese<PERSON>eerd"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Selecteer een kanaal", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "BUTTON_TEXT": "<PERSON><PERSON><PERSON> kop<PERSON>en", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Aandacht vereist", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "Het kanaal is succesvol verbonden", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "<PERSON><PERSON> hier om deel te nemen", "LEAVE_THE_ROOM": "Gesprek verlaten", "START_VIDEO_CALL_HELP_TEXT": "Start een nieuw videogesprek met de klant", "JOIN_ERROR": "There was an error joining the call, please try again", "CREATE_ERROR": "There was an error creating a meeting link, please try again"}, "OPEN_AI": {"AI_ASSIST": "AI Assistent", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Antwoord suggestie", "SUMMARIZE": "<PERSON><PERSON><PERSON><PERSON>", "REPHRASE": "<PERSON><PERSON><PERSON><PERSON>", "FIX_SPELLING_GRAMMAR": "Repareer spelling en grammatica", "SHORTEN": "Verkorten", "EXPAND": "Uitklappen", "MAKE_FRIENDLY": "<PERSON><PERSON><PERSON><PERSON> berichttoon naar vriendelijk", "MAKE_FORMAL": "Gebruik formele toon", "SIMPLIFY": "Vereenvoudigen"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Concept inhoud", "GENERATED_TITLE": "Gegenereerde inhoud", "AI_WRITING": "AI schrijft", "BUTTONS": {"APPLY": "De<PERSON> <PERSON>ie geb<PERSON>iken", "CANCEL": "<PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "Integreer met OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Voer je OpenAI API-sleutel in", "BUTTONS": {"NEED_HELP": "<PERSON>lp nodig?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Installatie voltooien"}, "DISMISS_MESSAGE": "U kunt OpenAI integratie later instellen wanneer u wilt.", "SUCCESS_MESSAGE": "OpenAI integratie installatie succesvol"}, "TITLE": "<PERSON><PERSON><PERSON><PERSON> met AI", "SUMMARY_TITLE": "<PERSON><PERSON><PERSON><PERSON> met AI", "REPLY_TITLE": "Antwoord suggesties met AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON>n", "OPTIONS": {"PROFESSIONAL": "Professioneel", "FRIENDLY": "Vriendelijk"}}, "BUTTONS": {"GENERATE": "<PERSON><PERSON>", "GENERATING": "Genereren...", "CANCEL": "<PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Verwijderen", "API": {"SUCCESS_MESSAGE": "Integratie succesvol verwijderd"}}, "CONNECT": {"BUTTON_TEXT": "Verbinden"}, "DASHBOARD_APPS": {"TITLE": "Dashboard apps", "HEADER_BTN_TXT": "Voeg een nieuwe dashboard app toe", "SIDEBAR_TXT": "<p><b>Dashboard Apps</b></p><p>Dashboard Apps allow organizations to embed an application inside the Chatwoot dashboard to provide the context for customer support agents. This feature allows you to create an application independently and embed that inside the dashboard to provide user information, their orders, or their previous payment history.</p><p>When you embed your application using the dashboard in Chatwoot, your application will get the context of the conversation and contact as a window event. Implement a listener for the message event on your page to receive the context.</p><p>To add a new dashboard app, click on the button 'Add a new dashboard app'.</p>", "DESCRIPTION": "Dashboard Apps allow organizations to embed an application inside the dashboard to provide the context for customer support agents. This feature allows you to create an application independently and embed that to provide user information, their orders, or their previous payment history.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "There are no dashboard apps configured on this account yet", "LOADING": "Fetching dashboard apps...", "TABLE_HEADER": {"NAME": "<PERSON><PERSON>", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Bewerk app", "DELETE_TOOLTIP": "App verwij<PERSON>en"}, "FORM": {"TITLE_LABEL": "<PERSON><PERSON>", "TITLE_PLACEHOLDER": "Enter a name for your dashboard app", "TITLE_ERROR": "A name for the dashboard app is required", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Enter the endpoint URL where your app is hosted", "URL_ERROR": "A valid URL is required"}, "CREATE": {"HEADER": "Voeg een nieuwe dashboard app toe", "FORM_SUBMIT": "Bevestigen", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Dashboard app configured successfully", "API_ERROR": "We couldn't create an app. Please try again later"}, "UPDATE": {"HEADER": "Edit dashboard app", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Dashboard app updated successfully", "API_ERROR": "We couldn't update the app. Please try again later"}, "DELETE": {"CONFIRM_YES": "Ja, verwijderen", "CONFIRM_NO": "N<PERSON>, behouden", "TITLE": "Verwijdering bevestigen", "MESSAGE": "Weet u zeker dat u de app - {appName} wilt verwijderen?", "API_SUCCESS": "Dashboard app deleted successfully", "API_ERROR": "We couldn't delete the app. Please try again later"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Aanmaken", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Link", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Titel", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Title is required"}, "DESCRIPTION": {"LABEL": "Beschrijving", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Team", "PLACEHOLDER": "Selecteer team", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Prioriteit", "PLACEHOLDER": "Selecteer prioriteit", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Label", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Status", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Aanmaken", "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Status", "PRIORITY": "Prioriteit", "ASSIGNEE": "Assignee", "LABELS": "<PERSON>en", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "NO_LINKED_ISSUES": "No linked issues found", "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Ja, verwijderen", "CANCEL": "<PERSON><PERSON><PERSON>"}, "CTA": {"TITLE": "Connect to Linear", "AGENT_DESCRIPTION": "Linear workspace is not connected. Request your administrator to connect a workspace to use this integration.", "DESCRIPTION": "Linear workspace is not connected. Click the button below to connect your workspace to use this integration.", "BUTTON_TEXT": "Connect Linear workspace"}}, "NOTION": {"DELETE": {"TITLE": "Are you sure you want to delete the Notion integration?", "MESSAGE": "Deleting this integration will remove access to your Notion workspace and stop all related functionality.", "CONFIRM": "Ja, verwijderen", "CANCEL": "<PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"TITLE": "Copilot", "TRY_THESE_PROMPTS": "Try these prompts", "PANEL_TITLE": "Get started with Copilot", "KICK_OFF_MESSAGE": "Need a quick summary, want to check past conversations, or draft a better reply? <PERSON><PERSON><PERSON>’s here to speed things up.", "SEND_MESSAGE": "Verstuur bericht...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "<PERSON><PERSON>", "USE": "Use this", "RESET": "Reset", "SHOW_STEPS": "Show steps", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}, "HIGH_PRIORITY": {"LABEL": "High priority conversations", "CONTENT": "Give me a summary of all high priority open conversations. Include the conversation ID, customer name (if available), last message content, and assigned agent. Group by status if relevant."}, "LIST_CONTACTS": {"LABEL": "List contacts", "CONTENT": "Show me the list of top 10 contacts. Include name, email or phone number (if available), last seen time, tags (if any)."}}}, "PLAYGROUND": {"USER": "<PERSON><PERSON>", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Typ uw bericht...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "Aanmaken", "EDIT": "<PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "NO_ASSISTANTS_AVAILABLE": "There are no assistants available in your account.", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Ja, verwijderen", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Features", "TOOLS": "Tools "}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "TEMPERATURE": {"LABEL": "Response Temperature", "DESCRIPTION": "Adjust how creative or restrictive the assistant's responses should be. Lower values produce more focused and deterministic responses, while higher values allow for more creative and varied outputs."}, "DESCRIPTION": {"LABEL": "Beschrijving", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Features", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Ja, verwijderen", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Verwijderen", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Ja, verwijderen", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Allemaal"}, "STATUS": {"TITLE": "Status", "PENDING": "Afwachtend", "APPROVED": "Approved", "ALL": "Allemaal"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Verbinding verbreken"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Ja, verwijderen", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Postvak In", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}