{"INBOX_MGMT": {"HEADER": "Inboxen", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "Er zijn geen inboxen aan dit account gekoppeld."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "<PERSON><PERSON> kanaal", "BODY": "<PERSON><PERSON> de a<PERSON>der die je wilt integreren met <PERSON><PERSON><PERSON><PERSON>."}, "INBOX": {"TITLE": "Maak inbox", "BODY": "Verifieer je account en maak een inbox."}, "AGENT": {"TITLE": "<PERSON><PERSON>n", "BODY": "Voeg agenten toe aan de aangemaakte inbox."}, "FINISH": {"TITLE": "Voilà!", "BODY": "Je bent he<PERSON>aal klaar om te beginnen!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Enter your inbox name (eg: Acme Inc)", "ERROR": "Please enter a valid inbox name"}, "WEBSITE_NAME": {"LABEL": "Website naam", "PLACEHOLDER": "<PERSON><PERSON><PERSON> de na<PERSON> van je website in (bv. Acme Inc)"}, "FB": {"HELP": "PS: Door in te loggen krijgen we alleen toegang tot berichten van uw Pagina. Chatwood heeft nooit toegang tot je privéberichten.", "CHOOSE_PAGE": "<PERSON><PERSON> pagina", "CHOOSE_PLACEHOLDER": "Select a page from the list", "INBOX_NAME": "Inbox Name", "ADD_NAME": "Add a name for your inbox", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "Pick a value", "CREATE_INBOX": "Maak inbox"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continue with Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Connect your Instagram Profile", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "Om uw Twitterprofiel als kanaal toe te voegen moet u uw Twitterprofiel verifiëren door te klikken op 'Meld je aan met Twitter' ", "ERROR_MESSAGE": "There was an error connecting to Twitter, please try again", "TWEETS": {"ENABLE": "Create conversations from mentioned Tweets"}}, "WEBSITE_CHANNEL": {"TITLE": "Website kanaal", "DESC": "Maak een kanaal voor uw website en begin met het ondersteunen van uw klanten via onze website widget.", "LOADING_MESSAGE": "Website ondersteuningskanaal aanmaken", "CHANNEL_AVATAR": {"LABEL": "<PERSON><PERSON> kanaal"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "<PERSON><PERSON><PERSON> een geldige URL in"}, "CHANNEL_DOMAIN": {"LABEL": "Website domein", "PLACEHOLDER": "Voer uw website domein in (bv. acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "Welkom op titel", "PLACEHOLDER": "Hallo daar!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Welkom Tagline", "PLACEHOLDER": "We maken het eenvoudig om met ons te verbinden. Vraag ons iets of deel uw feedback."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Channel greeting message", "PLACEHOLDER": "Acme Inc reageert meestal binnen een paar uur."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Enable channel greeting", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "REPLY_TIME": {"TITLE": "Set Reply time", "IN_A_FEW_MINUTES": "In enkele minuten", "IN_A_FEW_HOURS": "Over een paar uur", "IN_A_DAY": "In één dag", "HELP_TEXT": "This reply time will be displayed on the live chat widget"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON><PERSON> van widget", "PLACEHOLDER": "Update de widget kleur gebruikt in widget"}, "SUBMIT_BUTTON": "Postvak in maken", "API": {"ERROR_MESSAGE": "We were not able to create a website channel, please try again"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Channel", "DESC": "Integrate Twilio and start supporting your customers via SMS or WhatsApp.", "ACCOUNT_SID": {"LABEL": "Klant SID", "PLACEHOLDER": "<PERSON><PERSON><PERSON> uw <PERSON><PERSON> Account-ID in", "ERROR": "Dit veld is verplicht"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "Dit veld is verplicht"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "Dit veld is verplicht"}, "MESSAGING_SERVICE_SID": {"LABEL": "Messaging Service SID", "PLACEHOLDER": "Please enter your Twilio Messaging Service SID", "ERROR": "Dit veld is verplicht", "USE_MESSAGING_SERVICE": "Use a Twilio Messaging Service"}, "CHANNEL_TYPE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "ERROR": "Selecteer uw kana<PERSON><PERSON>"}, "AUTH_TOKEN": {"LABEL": "Authenticat<PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON> in", "ERROR": "Dit veld is verplicht"}, "CHANNEL_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "Dit veld is verplicht"}, "PHONE_NUMBER": {"LABEL": "Telefoonnummer", "PLACEHOLDER": "Voer het telefoonnummer in waaruit het bericht wordt verzonden.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "U moet de callback URL voor berichten configureren in Twilio met de URL die hier wordt vermeld."}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON>-kana<PERSON> a<PERSON>", "API": {"ERROR_MESSAGE": "Het is niet gelukt om Twilio referenties te verifiëren, probeer het opnieuw"}}, "SMS": {"TITLE": "SMS-kanaal", "DESC": "Start supporting your customers via SMS.", "PROVIDERS": {"LABEL": "API-aanbieder", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandbreedte"}, "API": {"ERROR_MESSAGE": "Het SMS-kanaal kon niet worden opgeslagen"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Account ID", "PLACEHOLDER": "Voer uw bandbreedte account-ID in", "ERROR": "Dit veld is verplicht"}, "API_KEY": {"LABEL": "API sleutel", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "Dit veld is verplicht"}, "API_SECRET": {"LABEL": "API-geheim", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "Dit veld is verplicht"}, "APPLICATION_ID": {"LABEL": "Applicatie-ID", "PLACEHOLDER": "Please enter your Bandwidth Application ID", "ERROR": "Dit veld is verplicht"}, "INBOX_NAME": {"LABEL": "Inbox Naam", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "Dit veld is verplicht"}, "PHONE_NUMBER": {"LABEL": "Telefoon nummer", "PLACEHOLDER": "Voer het telefoonnummer in waaruit het bericht wordt verzonden.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "SUBMIT_BUTTON": "Create Bandwidth Channel", "API": {"ERROR_MESSAGE": "We were not able to authenticate Bandwidth credentials, please try again"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Bandwidth with the URL mentioned here."}}}, "WHATSAPP": {"TITLE": "WhatsApp Channel", "DESC": "Start supporting your customers via WhatsApp.", "PROVIDERS": {"LABEL": "API-aanbieder", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360dialog"}, "INBOX_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Please enter an inbox name", "ERROR": "Dit veld is verplicht"}, "PHONE_NUMBER": {"LABEL": "Telefoon nummer", "PLACEHOLDER": "Voer het telefoonnummer in waaruit het bericht wordt verzonden.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "PHONE_NUMBER_ID": {"LABEL": "Phone number ID", "PLACEHOLDER": "Please enter the Phone number ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Business Account ID", "PLACEHOLDER": "Please enter the Business Account ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Verify Token", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Please enter a valid value."}, "API_KEY": {"LABEL": "API-sleutel", "SUBTITLE": "Configure the WhatsApp API key.", "PLACEHOLDER": "API-sleutel", "ERROR": "Please enter a valid value."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL and the verification token in the Facebook Developer portal with the values shown below.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verification Token"}, "SUBMIT_BUTTON": "Create WhatsApp Channel", "API": {"ERROR_MESSAGE": "We were not able to save the WhatsApp channel"}}, "VOICE": {"TITLE": "Voice Channel", "DESC": "Integrate Twilio Voice and start supporting your customers via phone calls.", "PHONE_NUMBER": {"LABEL": "Telefoonnummer", "PLACEHOLDER": "Enter your phone number (e.g. +**********)", "ERROR": "Please provide a valid phone number in E.164 format (e.g. +**********)"}, "TWILIO": {"ACCOUNT_SID": {"LABEL": "Klant SID", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON> Account SID", "REQUIRED": "Account SID is required"}, "AUTH_TOKEN": {"LABEL": "Authenticat<PERSON>", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON>", "REQUIRED": "Auth Token is required"}, "API_KEY_SID": {"LABEL": "API Key SID", "PLACEHOLDER": "Enter your Twilio API Key SID", "REQUIRED": "API Key SID is required"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Enter your Twilio API Key Secret", "REQUIRED": "API Key Secret is required"}, "TWIML_APP_SID": {"LABEL": "TwiML App SID", "PLACEHOLDER": "Enter your Twilio TwiML App SID (starts with AP)", "REQUIRED": "TwiML App SID is required"}}, "SUBMIT_BUTTON": "Create Voice Channel", "API": {"ERROR_MESSAGE": "We were not able to create the voice channel"}}, "API_CHANNEL": {"TITLE": "API-kanaal", "DESC": "Integrate with API channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> naam", "PLACEHOLDER": "<PERSON><PERSON>r een kanaal naam in", "ERROR": "Dit veld is verplicht"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Create API Channel", "API": {"ERROR_MESSAGE": "We were not able to save the api channel"}}, "EMAIL_CHANNEL": {"TITLE": "Email Channel", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> naam", "PLACEHOLDER": "<PERSON><PERSON>r een kanaal naam in", "ERROR": "Dit veld is verplicht"}, "EMAIL": {"LABEL": "E-mailadres", "SUBTITLE": "Email where your customers sends you support tickets", "PLACEHOLDER": "E-mailadres"}, "SUBMIT_BUTTON": "Create Email Channel", "API": {"ERROR_MESSAGE": "We were not able to save the email channel"}, "FINISH_MESSAGE": "Start forwarding your emails to the following email address."}, "LINE_CHANNEL": {"TITLE": "LINE-kanaal", "DESC": "Integrate with LINE channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> naam", "PLACEHOLDER": "<PERSON><PERSON>r een kanaal naam in", "ERROR": "Dit veld is verplicht"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Channel ID", "PLACEHOLDER": "LINE Channel ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Channel Token", "PLACEHOLDER": "LINE Channel Token"}, "SUBMIT_BUTTON": "Create LINE Channel", "API": {"ERROR_MESSAGE": "We were not able to save the LINE channel"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL in LINE application with the URL mentioned here."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Channel", "DESC": "Integrate with Telegram channel and start supporting your customers.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure the bot token you have obtained from Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Create Telegram Channel", "API": {"ERROR_MESSAGE": "We were not able to save the telegram channel"}}, "AUTH": {"TITLE": "<PERSON>es een kanaal", "DESC": "Chatwoot supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "<PERSON><PERSON>", "DESC": "Hier kunt u agenten toevoegen om uw nieuw gecreëerde inbox te beheren. Alleen deze agenten hebben toegang tot uw inbox. Agents die geen deel uitmaken van dit postvak in zullen niet in staat zijn om berichten in dit postvak te zien of te reageren wanneer ze inloggen. <br> <b>PS:</b> Als beheerder als u toegang wilt krijgen tot alle inboxen, voeg jezelf toe als agent aan alle inboxen die je maakt.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Pick agents for the inbox"}, "DETAILS": {"TITLE": "Details inbox", "DESC": "Selecteer de Facebook-pagina die je wilt verbinden met <PERSON><PERSON><PERSON> uit de dropdown hieronder. U kunt ook een aangepaste naam geven aan uw inbox voor een betere identificatie."}, "FINISH": {"TITLE": "Geïnstalleerd het!", "DESC": "U heeft de integratie van uw Facebook-pagina met <PERSON><PERSON><PERSON> succesvol voltooid. Volgende keer dat een klant uw pagina bericht, zal het gesprek automatisch in uw inbox verschijnen.<br>We bieden u ook een widget script aan, dat u eenvoudig aan uw website kunt toevoegen. Zodra dit live is op je website klanten kunnen vanaf uw website een bericht sturen zonder de hulp van een externe tool en het gesprek verschijnt hier op Chatwoot.<br><PERSON><PERSON>, toch? Wel, proberen we zeker om te zijn :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "<PERSON><PERSON><PERSON><PERSON><PERSON> met Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "Er ging iets mis, gelieve pagina te vernieuwen...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "Uw postvak wordt gemaakt...", "TITLE": "<PERSON><PERSON><PERSON><PERSON> in details", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Voeg agenten toe", "ADD_AGENTS": "Agents toevoegen aan uw Postvak IN..."}, "FINISH": {"TITLE": "Uw postvak is klaar!", "MESSAGE": "Je kunt nu contact opnemen met je k<PERSON><PERSON> via het nieuwe Kanaal. Gelukkige ondersteuning", "BUTTON_TEXT": "Breng me ernaar toe", "MORE_SETTINGS": "More settings", "WEBSITE_SUCCESS": "Het aanmaken van een website kanaal is gelukt. Kopieer de code hieronder weergegeven en plak deze op uw website. De volgende keer dat een klant de live chat gebruikt, verschijnt het gesprek automatisch op uw inbox."}, "REAUTH": "Autoriseer", "VIEW": "Bekijken", "EDIT": {"API": {"SUCCESS_MESSAGE": "Inbox instellingen succesvol bijgewerkt", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Automatische toewijzing succesvol bijgewerkt", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "ENABLE_CSAT": {"ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Vriendelijk", "FROM": "van", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professioneel", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Opsla<PERSON>"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "Ingeschakeld", "DISABLED": "Uitgeschakeld"}, "ENABLE_HMAC": {"LABEL": "Enable"}}, "DELETE": {"BUTTON_TEXT": "Verwijderen", "AVATAR_DELETE_BUTTON_TEXT": "Delete Avatar", "CONFIRM": {"TITLE": "Verwijdering bevestigen", "MESSAGE": "Weet u zeker dat u wilt verwijderen ", "PLACE_HOLDER": "Please type {inboxName} to confirm", "YES": "Ja, verwijderen ", "NO": "Nee, Behouden "}, "API": {"SUCCESS_MESSAGE": "Inbox succesvol verwijderd", "ERROR_MESSAGE": "Postvak verwijderen mislukt. Probeer het later opnieuw.", "AVATAR_SUCCESS_MESSAGE": "Inbox avatar deleted successfully", "AVATAR_ERROR_MESSAGE": "Could not delete the inbox avatar. Please try again later."}}, "TABS": {"SETTINGS": "Instellingen", "COLLABORATORS": "Collaborators", "CONFIGURATION": "Configuration", "CAMPAIGN": "Campagnes", "PRE_CHAT_FORM": "Pre Chat Form", "BUSINESS_HOURS": "Business Hours", "WIDGET_BUILDER": "Widget Builder", "BOT_CONFIGURATION": "Bot Configuration", "CSAT": "CSAT"}, "SETTINGS": "Instellingen", "FEATURES": {"LABEL": "Features", "DISPLAY_FILE_PICKER": "Display file picker on the widget", "DISPLAY_EMOJI_PICKER": "Display emoji picker on the widget", "ALLOW_END_CONVERSATION": "Allow users to end conversation from the widget", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON>", "MESSENGER_SUB_HEAD": "Plaats deze knop in je lichaam tag", "INBOX_AGENTS": "<PERSON><PERSON>", "INBOX_AGENTS_SUB_TEXT": "Voeg agenten toe of verwijder ze uit deze inbox", "AGENT_ASSIGNMENT": "Conversation Assignment", "AGENT_ASSIGNMENT_SUB_TEXT": "Update conversation assignment settings", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Enable email collect box", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Enable or disable email collect box on new conversation", "AUTO_ASSIGNMENT": "Automatische toewijzing inschakelen", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "Enable conversation continuity via email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "Conversations will continue over email if the contact email address is available.", "LOCK_TO_SINGLE_CONVERSATION": "Lock to single conversation", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Enable or disable multiple conversations for the same contact in this inbox", "INBOX_UPDATE_TITLE": "Instellingen Postvak In", "INBOX_UPDATE_SUB_TEXT": "Update uw inbox instellingen", "AUTO_ASSIGNMENT_SUB_TEXT": "In- of uitschakelen van de automatische toewijzing van nieuwe gesprekken aan de agenten die aan deze inbox zijn toegevoegd.", "HMAC_VERIFICATION": "User Identity Validation", "HMAC_DESCRIPTION": "In order to validate the user's identity, you can pass an `identifier_hash` for each user. You can generate a HMAC sha256 hash using the `identifier` with the key shown here.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "Enforce User Identity Validation", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Inbox Identifier", "INBOX_IDENTIFIER_SUB_TEXT": "Use the `inbox_identifier` token shown here to authentication your API clients.", "FORWARD_EMAIL_TITLE": "Forward to Email", "FORWARD_EMAIL_SUB_TEXT": "Start forwarding your emails to the following email address.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Allow messages after conversation resolved", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Allow the end-users to send messages even after the conversation is resolved.", "WHATSAPP_SECTION_SUBHEADER": "This API Key is used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API sleutel", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verification Token", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Auto assignment limit", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Please enter a value greater than 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limit the maximum number of conversations from this inbox that can be auto assigned to an agent"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Autoriseer", "SUBTITLE": "Your Facebook connection has expired, please reconnect your Facebook page to continue services", "MESSAGE_SUCCESS": "Reconnection successful", "MESSAGE_ERROR": "Er is een fout opgetreden, probeer het opnieuw"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pre chat forms enable you to capture user information before they start conversation with you.", "SET_FIELDS": "Pre chat form fields", "SET_FIELDS_HEADER": {"FIELDS": "Fields", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "<PERSON><PERSON><PERSON><PERSON>", "TYPE": "Type", "REQUIRED": "Required"}, "ENABLE": {"LABEL": "Enable pre chat form", "OPTIONS": {"ENABLED": "Yes", "DISABLED": "No"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pre chat message", "PLACEHOLDER": "This message would be visible to the users along with the form"}, "REQUIRE_EMAIL": {"LABEL": "Visitors should provide their name and email address before starting the chat"}}, "CSAT": {"TITLE": "Enable CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "Bericht", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "bevat", "DOES_NOT_CONTAINS": "bevat niet"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Set your availability", "SUBTITLE": "Set your availability on your livechat widget", "WEEKLY_TITLE": "Set your weekly hours", "TIMEZONE_LABEL": "Select timezone", "UPDATE": "Update business hours settings", "TOGGLE_AVAILABILITY": "Enable business availability for this inbox", "UNAVAILABLE_MESSAGE_LABEL": "Unavailable message for visitors", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Enable availability for this day", "UNAVAILABLE": "Unavailable", "HOURS": "hours", "VALIDATION_ERROR": "Starting time should be before closing time.", "CHOOSE": "<PERSON><PERSON>"}, "ALL_DAY": "All-Day"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Set your IMAP details", "NOTE_TEXT": "To enable SMTP, please configure IMAP.", "UPDATE": "Update IMAP settings", "TOGGLE_AVAILABILITY": "Enable IMAP configuration for this inbox", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP settings updated successfully", "ERROR_MESSAGE": "Unable to update IMAP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Inloggen", "PLACE_HOLDER": "Inloggen"}, "PASSWORD": {"LABEL": "Wachtwoord", "PLACE_HOLDER": "Wachtwoord"}, "ENABLE_SSL": "Enable SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Set your SMTP details", "UPDATE": "Update SMTP settings", "TOGGLE_AVAILABILITY": "Enable SMTP configuration for this inbox", "TOGGLE_HELP": "Enabling SMTP will help the user to send email", "EDIT": {"SUCCESS_MESSAGE": "SMTP settings updated successfully", "ERROR_MESSAGE": "Unable to update SMTP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Inloggen", "PLACE_HOLDER": "Inloggen"}, "PASSWORD": {"LABEL": "Wachtwoord", "PLACE_HOLDER": "Wachtwoord"}, "DOMAIN": {"LABEL": "Domeinnaam", "PLACE_HOLDER": "Domeinnaam"}, "ENCRYPTION": "Encryption", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Mode", "AUTH_MECHANISM": "Authentication"}, "NOTE": "Note: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar deleted successfully", "ERROR_MESSAGE": "Er is een fout opgetreden, probeer het opnieuw"}}}, "WEBSITE_NAME": {"LABEL": "Website naam", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> de na<PERSON> van je website in (bv. Acme Inc)", "ERROR": "Please enter a valid website name"}, "WELCOME_HEADING": {"LABEL": "Welkom op titel", "PLACE_HOLDER": "Hi there!"}, "WELCOME_TAGLINE": {"LABEL": "Welkom Tagline", "PLACE_HOLDER": "We maken het eenvoudig om met ons te verbinden. Vraag ons iets of deel uw feedback."}, "REPLY_TIME": {"LABEL": "Reply Time", "IN_A_FEW_MINUTES": "In enkele minuten", "IN_A_FEW_HOURS": "Over een paar uur", "IN_A_DAY": "In één dag"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON><PERSON> van widget", "WIDGET_BUBBLE_POSITION_LABEL": "Widget Bubble Position", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Type", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "<PERSON><PERSON> met ons", "LABEL": "Widget Bubble Launcher Title", "PLACE_HOLDER": "<PERSON><PERSON> met ons"}, "UPDATE": {"BUTTON_TEXT": "Update Widget Settings", "API": {"SUCCESS_MESSAGE": "Widget settings updated successfully", "ERROR_MESSAGE": "Unable to update widget settings"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Preview", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Left", "RIGHT": "Right"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standard", "EXPANDED_BUBBLE": "Expanded Bubble"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "<PERSON><PERSON><PERSON> meestal binnen een paar minuten", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON> meestal binnen een paar uur", "IN_A_DAY": "<PERSON><PERSON><PERSON> meestal binnen een dag"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "Start Chat", "CHAT_INPUT_PLACEHOLDER": "<PERSON><PERSON> uw bericht"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "We are Online", "OFFLINE": "We zijn momenteel afwezig"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hello"}, "BRANDING_TEXT": "Mogelijk gemaakt door Chatwoot", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "E-mailadres", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API-kanaal", "INSTAGRAM": "Instagram", "VOICE": "Voice"}}}