{"INBOX_MGMT": {"HEADER": "<PERSON><PERSON><PERSON> thư đ<PERSON>n", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "<PERSON><PERSON><PERSON><PERSON> có hộp thư đến nào đư<PERSON><PERSON> đ<PERSON>h kèm với tài khoản này."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON>", "BODY": "<PERSON><PERSON><PERSON> nhà cung cấp bạn muốn tích hợp v<PERSON><PERSON>."}, "INBOX": {"TITLE": "<PERSON><PERSON><PERSON> thư đến", "BODY": "<PERSON><PERSON><PERSON> thực tài k<PERSON>n của bạn và tạo hộp thư đến."}, "AGENT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> đại lý", "BODY": "<PERSON>hê<PERSON> đại lý vào hộp thư đến đã tạo."}, "FINISH": {"TITLE": "Voilà!", "BODY": "Bạn đã sẵn sàng để đi!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> hộ<PERSON> thư đến", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tên kênh của bạn (ví dụ: WebA)", "ERROR": "<PERSON><PERSON> lòng nhập tên kênh hợp lệ"}, "WEBSITE_NAME": {"LABEL": "<PERSON><PERSON><PERSON> trang <PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tên trang web của bạn (ví dụ: Acme Inc)"}, "FB": {"HELP": "Tái bút: Bằng cách đăng nhập, chúng tôi chỉ có quyền truy cập vào các tin nhắn trên Trang của bạn. Chatwoot không bao giờ có thể truy cập tin nhắn riêng tư của bạn.", "CHOOSE_PAGE": "<PERSON><PERSON><PERSON> trang", "CHOOSE_PLACEHOLDER": "<PERSON><PERSON><PERSON> một trang từ danh sách", "INBOX_NAME": "<PERSON><PERSON><PERSON> hộ<PERSON> thư đến", "ADD_NAME": "<PERSON><PERSON><PERSON><PERSON> tên cho hộp thư đến của bạn", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "<PERSON><PERSON><PERSON> một giá trị", "CREATE_INBOX": "<PERSON><PERSON><PERSON> thư đến"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continue with Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Connect your Instagram Profile", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "<PERSON><PERSON> thêm hồ sơ Twitter của bạn làm kênh, bạn cần xác thực <PERSON><PERSON> sơ Twitter của mình bằng cách nhấp vào '<PERSON><PERSON>ng nhập bằng Twitter", "ERROR_MESSAGE": "<PERSON><PERSON> xảy ra lỗi khi kết nối đến Twitter, vui lòng thử lại", "TWEETS": {"ENABLE": "<PERSON><PERSON><PERSON> cu<PERSON>c trò chuyện từ các dòng Tweet đư<PERSON><PERSON> nhắn đến"}}, "WEBSITE_CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON> trang web", "DESC": "T<PERSON><PERSON> kênh cho trang web của bạn và bắt đầu hỗ trợ khách hàng của bạn thông qua widget trang web của chúng tôi.", "LOADING_MESSAGE": "T<PERSON><PERSON> kênh hỗ trợ trang web", "CHANNEL_AVATAR": {"LABEL": "<PERSON><PERSON><PERSON> đại di<PERSON>n kênh"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "<PERSON><PERSON> lòng nhậ<PERSON> một URL hợp lệ"}, "CHANNEL_DOMAIN": {"LABEL": "Domain trang web", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tên miền trang web của bạn (ví dụ: acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "<PERSON>i<PERSON><PERSON> đề chào mừng", "PLACEHOLDER": "Chào bạn !"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "<PERSON>òng giới thiệu chào mừng", "PLACEHOLDER": "<PERSON>úng tôi làm cho việc kết nối với chúng tôi trở nên đơn giản. Hỏi chúng tôi bất cứ điều gì hoặc chia sẻ phản hồi của bạn."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "<PERSON> nh<PERSON>n chúc mừng kênh", "PLACEHOLDER": "Acme Inc thường trả lời sau vài giờ."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "<PERSON><PERSON><PERSON> lời ch<PERSON>o kênh", "HELP_TEXT": "Tự động gửi tin nhắn chào mừng khi có hội thoại mới.", "ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "REPLY_TIME": {"TITLE": "Đặt thời gian phản hồi", "IN_A_FEW_MINUTES": "Trong một vài phút", "IN_A_FEW_HOURS": "Trong một vài giờ", "IN_A_DAY": "Trong một ngày", "HELP_TEXT": "Thời gian trả lời này sẽ được hiển thị trên tiện ích trò chuyện trực tiếp"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON><PERSON> ti<PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON> nhật màu tiện ích con đ<PERSON><PERSON><PERSON> sử dụng trong tiện ích con"}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> thư đến", "API": {"ERROR_MESSAGE": "<PERSON><PERSON>g tôi không thể tạo kênh trang web, vui lòng thử lại"}}, "TWILIO": {"TITLE": "<PERSON><PERSON><PERSON>/WhatsApp", "DESC": "<PERSON><PERSON><PERSON> hợ<PERSON><PERSON> và bắt đầu hỗ trợ khách hàng của bạn qua SMS hoặc WhatsApp.", "ACCOUNT_SID": {"LABEL": "Tài khoản SID", "PLACEHOLDER": "<PERSON><PERSON> lò<PERSON> nhập SID tài k<PERSON>n T<PERSON><PERSON> của bạn", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "MESSAGING_SERVICE_SID": {"LABEL": "Dịch vụ nhắn tin SID", "PLACEHOLDER": "<PERSON><PERSON> lò<PERSON> nhập SID của dịch vụ nhắn tin Twilio của bạn", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc", "USE_MESSAGING_SERVICE": "Sử dụng dịch vụ nhắn tin Twilio"}, "CHANNEL_TYPE": {"LABEL": "<PERSON><PERSON><PERSON> k<PERSON>", "ERROR": "<PERSON><PERSON> lòng chọn loại kênh của bạn"}, "AUTH_TOKEN": {"LABEL": "Token xác thực", "PLACEHOLDER": "<PERSON><PERSON> lòng nh<PERSON><PERSON><PERSON><PERSON>", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> hộ<PERSON> thư đến", "PLACEHOLDER": "<PERSON><PERSON> lòng điền tên hộp thư đến", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "PHONE_NUMBER": {"LABEL": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập số điện thoại mà tin nhắn sẽ được gửi.", "ERROR": "<PERSON>ui lòng cung cấp số điện thoại hợp lệ bắt đầu bằng dấu `+` và không chứa bất kỳ dấu cách nào."}, "API_CALLBACK": {"TITLE": "URL gọi lại", "SUBTITLE": "Bạn phải định cấu hình URL gọi lại tin nhắn trong Twilio với URL được đề cập ở đây."}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON>", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể xác thực thông tin đăng nhậ<PERSON>, vui lòng thử lại"}}, "SMS": {"TITLE": "Kênh SMS", "DESC": "<PERSON><PERSON>t đầu hỗ trợ khách hàng thông qua SMS.", "PROVIDERS": {"LABEL": "Nhà cung cấp API", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể lưu kênh <PERSON>"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "ID Tài khoản", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập ID Tài kho<PERSON>n Bandwidth", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "API_KEY": {"LABEL": "Khoá API", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "API_SECRET": {"LABEL": "<PERSON><PERSON>t <PERSON>", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "APPLICATION_ID": {"LABEL": "ID ứng dụng", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập ID Ứng dụng Bandwidth", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON> hộ<PERSON> thư đến", "PLACEHOLDER": "<PERSON><PERSON> lòng điền tên kênh", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "PHONE_NUMBER": {"LABEL": "Số Điệ<PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập số điện thoại mà tin nhắn sẽ được gửi.", "ERROR": "<PERSON>ui lòng cung cấp số điện thoại hợp lệ bắt đầu bằng dấu `+` và không chứa bất kỳ dấu cách nào."}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON>", "API": {"ERROR_MESSAGE": "<PERSON><PERSON>g tôi không thể xác thực \bchứng chỉ Bandwidth, vui lòng thử lại"}, "API_CALLBACK": {"TITLE": "URL gọi lại", "SUBTITLE": "Bạn phải cấu hình tin nhắn URL gọi lại trong Bandwith với URL đã được đề cập ở đây."}}}, "WHATSAPP": {"TITLE": "<PERSON><PERSON><PERSON>", "DESC": "<PERSON><PERSON><PERSON> đầu hỗ trợ khách hàng thông qua WhatsApp.", "PROVIDERS": {"LABEL": "Nhà cung cấp API", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "<PERSON><PERSON><PERSON> m<PERSON> WhatsApp", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON> hộ<PERSON> thư đến", "PLACEHOLDER": "<PERSON><PERSON> lòng điền tên kênh", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "PHONE_NUMBER": {"LABEL": "Số Điệ<PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập số điện thoại mà tin nhắn sẽ được gửi.", "ERROR": "<PERSON>ui lòng cung cấp số điện thoại hợp lệ bắt đầu bằng dấu `+` và không chứa bất kỳ dấu cách nào."}, "PHONE_NUMBER_ID": {"LABEL": "ID số điện thoại", "PLACEHOLDER": "<PERSON><PERSON> lò<PERSON> nhập ID số điện thoại lấy đư<PERSON><PERSON> từ trang tổng quan nhà phát triển Facebook.", "ERROR": "<PERSON><PERSON> lòng điền giá trị hợp lệ."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "ID tài k<PERSON>n do<PERSON>h ng<PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lò<PERSON> nhập ID tài kho<PERSON>n doanh nghiệp có được từ trang tổng quan dành cho nhà phát triển Facebook.", "ERROR": "<PERSON><PERSON> lòng điền giá trị hợp lệ."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Token x<PERSON>c thực <PERSON>", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "<PERSON><PERSON> lòng điền giá trị hợp lệ."}, "API_KEY": {"LABEL": "Khoá API", "SUBTITLE": "Cấu h<PERSON>nh Khoá API WhatsApp.", "PLACEHOLDER": "Khoá API", "ERROR": "<PERSON><PERSON> lòng điền giá trị hợp lệ."}, "API_CALLBACK": {"TITLE": "URL gọi lại", "SUBTITLE": "Bạn phải định cấu hình URL webhook và mã xác minh trong cổng Nhà phát triển của Facebook với các giá trị được hiển thị bên dưới.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Mã xác minh Webhook"}, "SUBMIT_BUTTON": "T<PERSON><PERSON> k<PERSON>nh <PERSON>s<PERSON>pp", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể lưu kênh WhatsApp"}}, "VOICE": {"TITLE": "Voice Channel", "DESC": "Integrate Twilio Voice and start supporting your customers via phone calls.", "PHONE_NUMBER": {"LABEL": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "PLACEHOLDER": "Enter your phone number (e.g. +**********)", "ERROR": "Please provide a valid phone number in E.164 format (e.g. +**********)"}, "TWILIO": {"ACCOUNT_SID": {"LABEL": "Tài khoản SID", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON> Account SID", "REQUIRED": "Account SID is required"}, "AUTH_TOKEN": {"LABEL": "Token xác thực", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON>", "REQUIRED": "Auth Token is required"}, "API_KEY_SID": {"LABEL": "API Key SID", "PLACEHOLDER": "Enter your Twilio API Key SID", "REQUIRED": "API Key SID is required"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Enter your Twilio API Key Secret", "REQUIRED": "API Key Secret is required"}, "TWIML_APP_SID": {"LABEL": "TwiML App SID", "PLACEHOLDER": "Enter your Twilio TwiML App SID (starts with AP)", "REQUIRED": "TwiML App SID is required"}}, "SUBMIT_BUTTON": "Create Voice Channel", "API": {"ERROR_MESSAGE": "We were not able to create the voice channel"}}, "API_CHANNEL": {"TITLE": "Kênh API", "DESC": "<PERSON><PERSON><PERSON> hợp với kênh <PERSON> và bắt đầu hỗ trợ khách hàng của bạn.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập tên kênh", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Tạo kênh API", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể lưu kênh api"}}, "EMAIL_CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON>", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON> k<PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập tên kênh", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "EMAIL": {"LABEL": "Email", "SUBTITLE": "Provide the email address where your customers send support requests.", "PLACEHOLDER": "Email"}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON>", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể lưu kênh <PERSON>"}, "FINISH_MESSAGE": "<PERSON><PERSON><PERSON> đầu chuyển tiếp email của bạn tới địa chỉ email sau."}, "LINE_CHANNEL": {"TITLE": "Kênh LINE", "DESC": "<PERSON><PERSON><PERSON> hợp với kênh LINE và bắt đầu hỗ trợ khách hàng của bạn.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập tên kênh", "ERROR": "Trư<PERSON>ng này là bắ<PERSON> buộc"}, "LINE_CHANNEL_ID": {"LABEL": "ID Kênh LINE", "PLACEHOLDER": "ID Kênh LINE"}, "LINE_CHANNEL_SECRET": {"LABEL": "<PERSON><PERSON> m<PERSON>t <PERSON>nh LINE", "PLACEHOLDER": "<PERSON><PERSON> m<PERSON>t <PERSON>nh LINE"}, "LINE_CHANNEL_TOKEN": {"LABEL": "Token Kênh LINE", "PLACEHOLDER": "Token Kênh LINE"}, "SUBMIT_BUTTON": "Tạo Kênh LINE", "API": {"ERROR_MESSAGE": "<PERSON><PERSON>g tôi không thể lưu kênh LINE"}, "API_CALLBACK": {"TITLE": "URL gọi lại", "SUBTITLE": "Bạn phải cấu hình webhook URL trong ứng dụng LINE với URL được nhắc đến ở đây."}}, "TELEGRAM_CHANNEL": {"TITLE": "Kênh Telegram", "DESC": "<PERSON><PERSON><PERSON> hợp vớ<PERSON> kênh Telegram và bắt đầu hỗ trợ khách hàng của bạn.", "BOT_TOKEN": {"LABEL": "Token <PERSON>", "SUBTITLE": "<PERSON><PERSON><PERSON> hình token bot mà bạn đã nhận đư<PERSON><PERSON> từ Telegram BotFather.", "PLACEHOLDER": "Token <PERSON>"}, "SUBMIT_BUTTON": "Tạo Kênh Telegram", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể lưu kênh \btelegram"}}, "AUTH": {"TITLE": "<PERSON><PERSON><PERSON>", "DESC": "Chatwoot hỗ trợ các tiện ích trò chuyện trự<PERSON> tiế<PERSON>, Facebook Messenger, h<PERSON> sơ Twitter, WhatsApp, Email, v. v., dưới dạng các kênh. Nếu bạn muốn xây dựng một kênh tùy chỉnh, bạn có thể tạo kênh này bằng cách sử dụng kênh API. <PERSON><PERSON> bắt đầu, hãy chọn một trong các kênh bên dưới."}, "AGENTS": {"TITLE": "Nhà Cung Cấp", "DESC": "Tại đây bạn có thể thêm các tác nhân để quản lý hộp thư đến mới tạo của mình. Chỉ những đại lý được chọn này mới có quyền truy cập vào hộp thư đến của bạn. Các nhân viên không thuộc hộp thư đến này sẽ không thể xem hoặc trả lời thư trong hộp thư đến này khi họ đăng nhập. <br> <b>PS:</b> Với tư cách là quản trị viên, nếu bạn cần quyền truy cập vào tất cả các hộp thư đến, bạn nên thêm mình làm đại lý cho tất cả các hộp thư đến mà bạn tạo.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "<PERSON><PERSON><PERSON> đại lý cho hộp thư đến"}, "DETAILS": {"TITLE": "<PERSON> tiết <PERSON><PERSON> thư đến", "DESC": "Từ menu thả xuống bên dư<PERSON>, chọ<PERSON>rang Facebook bạn muốn kết nối với Chatwoot. Bạn cũng có thể đặt tên tùy chỉnh cho hộp thư đến của mình để nhận dạng tốt hơn."}, "FINISH": {"TITLE": "<PERSON><PERSON>g r<PERSON>i!", "DESC": "Bạn đã tích hợp thành công Trang Facebook của mình với Chatwoot. Lần tới khi khách hàng nhắn tin cho Trang của bạn, cuộc trò chuyện sẽ tự động xuất hiện trong hộp thư đến của bạn.<br>Chúng tôi cũng đang cung cấp cho bạn tập lệnh widget mà bạn có thể dễ dàng thêm vào trang web của mình. Khi điều này xuất hiện trên trang web của bạn, khách hàng có thể nhắn tin cho bạn ngay từ trang web của bạn mà không cần sự trợ giúp của bất kỳ công cụ bên ngoài nào và cuộc trò chuyện sẽ xuất hiện ngay tại đây, trê<PERSON>woot.<br>Cool, huh? Well :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "<PERSON><PERSON><PERSON> thực bạn bằng Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "<PERSON><PERSON> x<PERSON>y ra sự cố, <PERSON><PERSON> lòng làm mới trang...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "<PERSON><PERSON><PERSON> thư đến của bạn...", "TITLE": "<PERSON><PERSON><PERSON> hình chi tiế<PERSON> hộ<PERSON> thư đến", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON> các nhà cung cấp", "ADD_AGENTS": "<PERSON><PERSON><PERSON><PERSON> các nhà cung cấp vào hộp thư đến của bạn..."}, "FINISH": {"TITLE": "<PERSON><PERSON><PERSON> thư đến của bạn đã sẵn sàng!", "MESSAGE": "<PERSON><PERSON><PERSON>, b<PERSON>n có thể tương tác với khách hàng thông qua Kênh mới của mình. <PERSON><PERSON><PERSON> vui vẻ ủng hộ", "BUTTON_TEXT": "<PERSON><PERSON><PERSON> cho tôi", "MORE_SETTINGS": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> chọn h<PERSON>n", "WEBSITE_SUCCESS": "Bạn đã hoàn thành việc tạo kênh trang web thành công. Sao chép mã được hiển thị bên dưới và dán vào trang web của bạn. <PERSON>ần tới khi khách hàng sử dụng cuộc trò chuyện trự<PERSON> tiế<PERSON>, cuộc trò chuyện sẽ tự động xuất hiện trong hộp thư đến của bạn."}, "REAUTH": "Ủy quyền lại", "VIEW": "Xem", "EDIT": {"API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> cập nhật cài đặt hộp thư đến thành công", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "<PERSON><PERSON> cập nhật thành công bài tập tự động", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON> tôi không thể cập nhật cài đặt hộp thư. <PERSON><PERSON> lòng thử lại sau."}, "EMAIL_COLLECT_BOX": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "ENABLE_CSAT": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Friendly", "FROM": "từ", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Save"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "ENABLE_HMAC": {"LABEL": "<PERSON><PERSON><PERSON>"}}, "DELETE": {"BUTTON_TEXT": "Xoá", "AVATAR_DELETE_BUTTON_TEXT": "<PERSON><PERSON><PERSON> hình đại di<PERSON>n", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON> n<PERSON>n xoá", "MESSAGE": "Bạn có muốn xoá? ", "PLACE_HOLDER": "<PERSON><PERSON> lòng điền {inboxName} để xác nhận", "YES": "Có, Xoá ", "NO": "Không, Giữ "}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> thư đến đã đư<PERSON>c xóa thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể xóa hộp thư đến. <PERSON><PERSON> lòng thử lại sau.", "AVATAR_SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> đại diện của hộp thư đến đã đư<PERSON><PERSON> xoá thành công", "AVATAR_ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể xoá hình đại diện của hộp thư đến. <PERSON><PERSON> lòng thử lại sau."}}, "TABS": {"SETTINGS": "Cài đặt", "COLLABORATORS": "<PERSON><PERSON><PERSON> tác viên", "CONFIGURATION": "<PERSON><PERSON><PERSON> h<PERSON>nh", "CAMPAIGN": "<PERSON><PERSON><PERSON>", "PRE_CHAT_FORM": "Biểu mẫu trước khi trò chuyện", "BUSINESS_HOURS": "<PERSON><PERSON><PERSON> làm việc", "WIDGET_BUILDER": "<PERSON><PERSON><PERSON><PERSON> tạo widget", "BOT_CONFIGURATION": "<PERSON><PERSON><PERSON> <PERSON>", "CSAT": "CSAT", "FACEBOOK_DATASET": "Facebook Dataset"}, "SETTINGS": "Cài đặt", "FEATURES": {"LABEL": "<PERSON><PERSON><PERSON>", "DISPLAY_FILE_PICKER": "<PERSON><PERSON><PERSON> thị bộ chọn tệp trên tiện ích con", "DISPLAY_EMOJI_PICKER": "<PERSON><PERSON><PERSON> thị bộ chọn biểu tượng cảm xúc trên tiện ích con", "ALLOW_END_CONVERSATION": "<PERSON> phép người dùng kết thúc cuộc trò chuyện từ công cụ chat", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON><PERSON><PERSON>", "MESSENGER_SUB_HEAD": "Đặt nút này bên trong thẻ body của bạn", "INBOX_AGENTS": "<PERSON><PERSON><PERSON> cung cấp", "INBOX_AGENTS_SUB_TEXT": "Thê<PERSON> hoặc xóa tác nhân khỏi hộp thư đến này", "AGENT_ASSIGNMENT": "<PERSON><PERSON> công cuộc trò chuy<PERSON>n", "AGENT_ASSIGNMENT_SUB_TEXT": "<PERSON><PERSON><PERSON> nhật phân công cuộc trò chuyện", "UPDATE": "<PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "<PERSON><PERSON><PERSON> hộp thu thập email", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "B<PERSON>t hoặc tắt hộp thu thập email trên cuộc trò chuyện mới", "AUTO_ASSIGNMENT": "<PERSON>ật tự động chuyển nhượng", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> tục cuộc trò chuyện qua email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "<PERSON><PERSON><PERSON><PERSON> trò chuyện sẽ tiế<PERSON> tụ<PERSON> qua email nếu có địa chỉ email liên lạc.", "LOCK_TO_SINGLE_CONVERSATION": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>c trò chuyện duy nhất", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Bật hoặc tắt nhiều cuộc trò chuyện cho cùng một liên hệ trong hộp thư đến này", "INBOX_UPDATE_TITLE": "<PERSON><PERSON>i đặt <PERSON><PERSON><PERSON> thư đến", "INBOX_UPDATE_SUB_TEXT": "<PERSON><PERSON><PERSON> nh<PERSON>t cài đặt hộp thư đến của bạn", "AUTO_ASSIGNMENT_SUB_TEXT": "Bật hoặc tắt tính năng tự động gán các cuộc hội thoại mới cho các tác nhân được thêm vào hộp thư đến này.", "HMAC_VERIFICATION": "<PERSON><PERSON><PERSON> thực danh t<PERSON>h người dùng", "HMAC_DESCRIPTION": "With this key you can generate a secret token that can be used to verify the identity of your users.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> thực danh t<PERSON>h ngư<PERSON>i dùng", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests that cannot be verified will be rejected.", "INBOX_IDENTIFIER": "<PERSON><PERSON><PERSON> danh hộp thư đến", "INBOX_IDENTIFIER_SUB_TEXT": "Dùng token định danh hộp thư đến hiện ở đây để xác thực các ứng dụng khách API của bạn.", "FORWARD_EMAIL_TITLE": "<PERSON><PERSON><PERSON><PERSON> tiếp đến <PERSON>", "FORWARD_EMAIL_SUB_TEXT": "<PERSON><PERSON><PERSON> đầu chuyển tiếp email của bạn tới địa chỉ email sau.", "ALLOW_MESSAGES_AFTER_RESOLVED": "<PERSON> phép nhắn tin sau khi cuộc trò chuyện đư<PERSON><PERSON> gi<PERSON>i quyết", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "<PERSON> phép người dùng cuối nhắn tin sau khi cuộc trò chuyện đư<PERSON><PERSON> gi<PERSON> quyết.", "WHATSAPP_SECTION_SUBHEADER": "Khóa API này được sử dụng để tích hợp với các API WhatsApp.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "Khoá API", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Mã xác minh Webhook", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "G<PERSON>ớ<PERSON> hạn tự động phân công tối đa", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Xin nhập v<PERSON>o một số lớn hơn 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Giới hạn số cuộc trò chuyện tối đa từ hộp thư này mà có thể được tự động phân công cho một hỗ trợ viên"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Ủy quyền lại", "SUBTITLE": "<PERSON><PERSON><PERSON> nối Facebook của bạn đã hết hạn, vui lòng kết nối lại trang Facebook của bạn để tiếp tục dịch vụ", "MESSAGE_SUCCESS": "<PERSON><PERSON><PERSON> n<PERSON>i lại thành công", "MESSAGE_ERROR": "<PERSON><PERSON> có lỗi, vui lòng thử lại"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Biểu mẫu trước khi trò chuyện cho phép bạn nắm bắt thông tin người dùng trước khi họ bắt đầu cuộc trò chuyện với bạn.", "SET_FIELDS": "<PERSON><PERSON><PERSON><PERSON> ô cần điền vào trước cuộc trò chuyện", "SET_FIELDS_HEADER": {"FIELDS": "Ô cần điền", "LABEL": "<PERSON><PERSON>ã<PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> từ hiện nền trong ô", "KEY": "Khoá", "TYPE": "<PERSON><PERSON><PERSON>", "REQUIRED": "<PERSON><PERSON><PERSON> b<PERSON>"}, "ENABLE": {"LABEL": "<PERSON><PERSON>t biểu mẫu trước khi trò chuyện", "OPTIONS": {"ENABLED": "<PERSON><PERSON>", "DISABLED": "K<PERSON>ô<PERSON>"}}, "PRE_CHAT_MESSAGE": {"LABEL": "<PERSON> nhắn tr<PERSON><PERSON><PERSON> khi trò chuyện", "PLACEHOLDER": "Tin nhắn này sẽ hiện cho những người dùng cùng với biểu mẫu"}, "REQUIRE_EMAIL": {"LABEL": "<PERSON><PERSON><PERSON><PERSON> truy cập nên cung cấp tên và địa chỉ email của họ trước khi bắt đầu trò chuyện"}}, "CSAT": {"TITLE": "Bật chỉ số đo lường sự hài lòng khách hàng", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "<PERSON>", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "ch<PERSON><PERSON>", "DOES_NOT_CONTAINS": "<PERSON><PERSON><PERSON><PERSON> bao gồm"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Đặt tính khả dụng của bạn", "SUBTITLE": "Đặt tính khả dụng của bạn trên tiện ích trò chuyện trực tiếp", "WEEKLY_TITLE": "Đặt gi<PERSON> hàng tuần của bạn", "TIMEZONE_LABEL": "<PERSON><PERSON><PERSON> múi giờ", "UPDATE": "<PERSON><PERSON><PERSON> nhật tuỳ chỉnh giờ làm việc", "TOGGLE_AVAILABILITY": "<PERSON><PERSON><PERSON> ho<PERSON>t t<PERSON>h khả dụng của do<PERSON>h nghiệp cho hộp thư đến này", "UNAVAILABLE_MESSAGE_LABEL": "Thông báo không có sẵn cho khách truy cập", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "<PERSON><PERSON><PERSON> ho<PERSON>t t<PERSON>h khả dụng cho hôm nay", "UNAVAILABLE": "<PERSON><PERSON><PERSON>ng có sẵn", "HOURS": "giờ", "VALIDATION_ERROR": "<PERSON><PERSON><PERSON> bắt đầu nên trước giờ đóng cửa.", "CHOOSE": "<PERSON><PERSON><PERSON>"}, "ALL_DAY": "<PERSON><PERSON>"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Đặt chi tiết IMAP", "NOTE_TEXT": "<PERSON><PERSON> bật chế độ <PERSON>, <PERSON><PERSON> h<PERSON><PERSON> c<PERSON>u hình IMAP.", "UPDATE": "<PERSON><PERSON><PERSON> nh<PERSON>t chi tiết IMAP", "TOGGLE_AVAILABILITY": "<PERSON><PERSON><PERSON> ho<PERSON> cấu hình IMAP cho hộp thư đến này", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "Tuỳ chỉnh IMAP đ<PERSON><PERSON><PERSON> cập nhật thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật tuỳ chỉnh IMAP"}, "ADDRESS": {"LABEL": "Địa chỉ", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> chỉ (Ví dụ: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "ENABLE_SSL": "Kích hoạt SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "STMP", "SUBTITLE": "Đặt chi tiết SMTP", "UPDATE": "<PERSON><PERSON><PERSON> nh<PERSON>t tuỳ chỉnh SMTP", "TOGGLE_AVAILABILITY": "<PERSON><PERSON><PERSON> ho<PERSON> c<PERSON><PERSON> hình SMTP cho hộp thư đến này", "TOGGLE_HELP": "<PERSON><PERSON><PERSON> SMTP sẽ giúp cho người dùng gửi email", "EDIT": {"SUCCESS_MESSAGE": "<PERSON><PERSON> chỉnh SM<PERSON> được cập nhật thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật tuỳ chỉnh SMTP"}, "ADDRESS": {"LABEL": "Địa chỉ", "PLACE_HOLDER": "Đ<PERSON><PERSON> chỉ (Ví dụ: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "DOMAIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "ENCRYPTION": "Mã hoá", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Mở Chế độ xác minh SSL", "AUTH_MECHANISM": "<PERSON><PERSON><PERSON>"}, "NOTE": "<PERSON><PERSON> chú: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "<PERSON><PERSON><PERSON> đ<PERSON> di<PERSON>n trang web", "DELETE": {"API": {"SUCCESS_MESSAGE": "<PERSON><PERSON>nh đại diện đã đư<PERSON>c xóa thành công", "ERROR_MESSAGE": "<PERSON><PERSON> có lỗi, vui lòng thử lại"}}}, "WEBSITE_NAME": {"LABEL": "<PERSON><PERSON><PERSON> trang <PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON> tên trang web của bạn (ví dụ: Acme Inc)", "ERROR": "<PERSON><PERSON> lòng nhập tên trang web hợp lệ"}, "WELCOME_HEADING": {"LABEL": "<PERSON>i<PERSON><PERSON> đề chào mừng", "PLACE_HOLDER": "Chào bạn!"}, "WELCOME_TAGLINE": {"LABEL": "<PERSON>òng giới thiệu chào mừng", "PLACE_HOLDER": "<PERSON>úng tôi làm cho việc kết nối với chúng tôi trở nên đơn giản. Hỏi chúng tôi bất cứ điều gì hoặc chia sẻ phản hồi của bạn."}, "REPLY_TIME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON> gian tr<PERSON> lời", "IN_A_FEW_MINUTES": "Trong một vài phút", "IN_A_FEW_HOURS": "Trong một vài giờ", "IN_A_DAY": "Trong một ngày"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON><PERSON> ti<PERSON>", "WIDGET_BUBBLE_POSITION_LABEL": "<PERSON><PERSON> trí bong bóng widget", "WIDGET_BUBBLE_TYPE_LABEL": "<PERSON><PERSON><PERSON> bong bóng widget", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "<PERSON><PERSON><PERSON> chuy<PERSON>n với chúng tôi", "LABEL": "Ti<PERSON><PERSON> đề trình khởi chạy bong bóng widget", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> chuy<PERSON>n với chúng tôi"}, "TYPING_TEXTS": {"LABEL": "<PERSON><PERSON><PERSON> b<PERSON>n Animation Typing", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tin nhắn để hiển thị...", "HELP_TEXT": "<PERSON>hững văn bản này sẽ xoay vòng liên tục trong bubble mở rộng để thu hút người dùng. Chỉ hoạt động với loại 'Expanded Bubble'."}, "UPDATE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON> nhật cài đặt widget", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> cập nhật cài đặt widget thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật cài đặt widget"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "<PERSON><PERSON>", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Trái", "RIGHT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "<PERSON><PERSON><PERSON>", "EXPANDED_BUBBLE": "Mở rộng bong bóng"}}, "WIDGET_SCREEN": {"DEFAULT": "Mặc định", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Thường trả lời sau vài phút", "IN_A_FEW_HOURS": "Thường trả lời sau vài giờ", "IN_A_DAY": "Thường trả lời trong một ngày"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "<PERSON><PERSON><PERSON> đầu một cuộc trò chuyện", "CHAT_INPUT_PLACEHOLDER": "<PERSON><PERSON> tin nhắn của bạn"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "<PERSON><PERSON><PERSON> tôi đang trự<PERSON> tuyến", "OFFLINE": "<PERSON><PERSON>n tại chúng tôi đang bận chút"}, "USER_MESSAGE": "<PERSON><PERSON> ch<PERSON>o", "AGENT_MESSAGE": "<PERSON><PERSON> ch<PERSON>o"}, "BRANDING_TEXT": "CC bởi Chatwoot", "SCRIPT_SETTINGS": "\nwindow.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "Email", "TELEGRAM": "Telegram", "LINE": "Line", "API": "Kênh API", "INSTAGRAM": "Instagram", "VOICE": "Voice"}, "FACEBOOK_DATASET": {"TITLE": "Facebook Dataset & Conversions API", "DESCRIPTION": "<PERSON> dõi nguồn tin nhắn từ quảng cáo Facebook và gửi sự kiện chuyển đổi về Facebook để tối ưu hóa quảng cáo tốt hơn. Điều này giúp bạn hiểu quảng cáo nào đang tạo ra cuộc trò chuyện và cải thiện ROI quảng cáo.", "ENABLE_TRACKING": "<PERSON><PERSON>t <PERSON> Facebook Dataset", "PIXEL_ID": "Facebook Pixel ID", "PIXEL_ID_PLACEHOLDER": "Nhập Facebook Pixel ID của bạn", "ACCESS_TOKEN": "Conversions API Access Token", "ACCESS_TOKEN_PLACEHOLDER": "Nhập Conversions API access token của bạn", "TEST_EVENT_CODE": "Test Event Code (<PERSON><PERSON><PERSON>)", "TEST_EVENT_CODE_PLACEHOLDER": "Nhập test event code để kiểm tra", "TEST_EVENT_CODE_HELP": "Sử dụng để kiểm tra sự kiện trong Facebook Events Manager tr<PERSON><PERSON><PERSON> khi triển khai", "DEFAULT_EVENT_NAME": "<PERSON><PERSON><PERSON> kiện Mặc định", "DEFAULT_EVENT_VALUE": "<PERSON><PERSON><PERSON> trị Sự kiện Mặc định", "DEFAULT_CURRENCY": "<PERSON><PERSON><PERSON><PERSON> tệ Mặc định", "AUTO_SEND_CONVERSIONS": "Tự động <PERSON>i Sự kiện Chu<PERSON>ển đổi", "AUTO_SEND_CONVERSIONS_HELP": "<PERSON><PERSON> đư<PERSON><PERSON> b<PERSON>, sự kiện chuyển đổi sẽ được gửi về Facebook tự động khi khách hàng nhắn tin từ quảng cáo", "TEST_CONNECTION": "<PERSON><PERSON><PERSON> tra <PERSON>", "SAVE_CONFIGURATION": "<PERSON><PERSON><PERSON>", "TRACKING_STATS": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON>", "TOTAL_TRACKINGS": "Tổng số <PERSON>", "CONVERSIONS_SENT": "Chuyển đổi <PERSON> gửi", "PENDING_CONVERSIONS": "Chuyển đổi Chờ xử lý", "UNIQUE_ADS": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON>", "RECENT_TRACKING_DATA": "<PERSON><PERSON> liệu <PERSON> đây", "REFRESH": "<PERSON><PERSON><PERSON>", "DATE": "<PERSON><PERSON><PERSON>", "CONTACT": "<PERSON><PERSON><PERSON>", "PLATFORM": "<PERSON><PERSON><PERSON> t<PERSON>", "AD_ID": "ID Quảng cáo", "CAMPAIGN_ID": "<PERSON> <PERSON> d<PERSON>ch", "ADSET_ID": "ID Nhóm quảng cáo", "REF_PARAMETER": "<PERSON><PERSON> s<PERSON>", "EVENT_NAME": "<PERSON><PERSON><PERSON> kiện", "CONVERSION_STATUS": "<PERSON>r<PERSON><PERSON> thái <PERSON> đổi", "ACTIONS": "<PERSON><PERSON><PERSON> đ<PERSON>", "SENT": "Đ<PERSON> gửi", "PENDING": "Chờ xử lý", "RESEND": "<PERSON><PERSON><PERSON> l<PERSON>i", "NO_TRACKING_DATA": "<PERSON><PERSON>a có dữ liệu theo dõi. Dữ liệu theo dõi sẽ xuất hiện ở đây khi khách hàng nhắn tin từ quảng cáo Facebook.", "LOAD_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể tải cấu hình Facebook Dataset", "SAVE_SUCCESS": "<PERSON><PERSON> lưu cấu hình Facebook Dataset thành công", "SAVE_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể lưu cấu hình Facebook Dataset", "TEST_SUCCESS": "<PERSON><PERSON>m tra kết nối Facebook Dataset thành công", "TEST_ERROR": "<PERSON><PERSON>m tra kết nối Facebook Dataset thất bại", "RESEND_SUCCESS": "Sự kiện chuyển đổi đã đư<PERSON><PERSON> đưa vào hàng đợi để gửi lại", "RESEND_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại sự kiện chuyển đổi", "PIXEL_SELECTION": "Chọn Facebook Pixel", "FETCH_PIXELS": "<PERSON><PERSON><PERSON> danh s<PERSON><PERSON>", "FETCHING_PIXELS": "<PERSON><PERSON>...", "SELECT_PIXEL": "<PERSON><PERSON><PERSON>", "PIXELS_LOADED": "<PERSON><PERSON> tải {count} pixels thành công", "PIXELS_FETCH_ERROR": "<PERSON><PERSON><PERSON>ng thể lấy danh sách Facebook Pixels. Vui lòng kiểm tra kết nối Facebook.", "USE_DROPDOWN": "Sử dụng dropdown", "ENTER_MANUALLY": "<PERSON><PERSON><PERSON><PERSON> thủ công", "EDIT_TOKEN": "Chỉnh sửa", "GENERATE_TOKEN": "<PERSON><PERSON><PERSON>ken tự động", "GENERATING_TOKEN": "<PERSON><PERSON> t<PERSON>...", "TOKEN_GENERATED": "Token đã đ<PERSON><PERSON><PERSON> tạo thành công", "TOKEN_GENERATION_ERROR": "<PERSON><PERSON>ông thể tạo access token. V<PERSON> lòng kiểm tra quyền Facebook.", "SELECT_PIXEL_FIRST": "<PERSON><PERSON> lòng chọn <PERSON> tr<PERSON>"}, "FACEBOOK_DATASET_REPORT": {"TITLE": "Phân tích Facebook Dataset", "DESCRIPTION": "<PERSON><PERSON> tích dữ liệu theo dõi quảng cáo Facebook và hiệu suất chuyển đổi để tối ưu hóa chiến dịch quảng cáo của bạn.", "DATE_RANGE": "<PERSON><PERSON><PERSON><PERSON> thời gian", "INBOX": "<PERSON><PERSON><PERSON> thư", "ALL_INBOXES": "<PERSON><PERSON><PERSON> cả hộp thư", "CONVERSION_STATUS": "<PERSON>r<PERSON><PERSON> thái chuyển đổi", "ALL_STATUS": "<PERSON><PERSON><PERSON> cả trạng thái", "SENT": "Đ<PERSON> gửi", "PENDING": "Chờ xử lý", "EXPORT": "<PERSON><PERSON><PERSON> dữ liệu", "REFRESH": "<PERSON><PERSON><PERSON>", "TOTAL_TRACKINGS": "Tổng số theo dõi", "CONVERSIONS_SENT": "Chuyển đổi đã gửi", "PENDING_CONVERSIONS": "Chuyển đổi chờ xử lý", "UNIQUE_CONTACTS": "<PERSON><PERSON><PERSON> duy nh<PERSON>t", "TOTAL_VALUE": "Tổng giá trị", "UNIQUE_ADS": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o duy nh<PERSON>t", "DAILY_TRACKING_TREND": "<PERSON> h<PERSON> theo dõi hàng ngày", "CONVERSION_RATES_BY_SOURCE": "Tỷ lệ chuyển đổi theo nguồn", "TOP_PERFORMING_ADS": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o hiệu suất cao nhất", "AD_TITLE": "Tiêu đề quảng cáo", "AD_ID": "ID quảng cáo", "CONVERSION_RATE": "Tỷ lệ chuyển đổi", "RECENT_TRACKING_DATA": "<PERSON><PERSON> liệu theo dõi gần đây", "BULK_RESEND": "<PERSON><PERSON><PERSON> lại hàng lo<PERSON>t", "DATE": "<PERSON><PERSON><PERSON>", "CONTACT": "<PERSON><PERSON><PERSON>", "REF_PARAMETER": "<PERSON><PERSON> s<PERSON>", "SOURCE": "<PERSON><PERSON><PERSON><PERSON>", "EVENT_VALUE": "<PERSON><PERSON><PERSON> trị sự kiện", "ACTIONS": "<PERSON><PERSON><PERSON> đ<PERSON>", "RESEND": "<PERSON><PERSON><PERSON> l<PERSON>i", "PREVIOUS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NEXT": "<PERSON><PERSON><PERSON><PERSON>", "PAGE_INFO": "Trang {current} của {total}", "NO_DATA": "<PERSON><PERSON><PERSON>ng có dữ liệu theo dõi Facebook dataset cho các bộ lọc đã chọn.", "LOAD_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể tải phân tích Facebook dataset", "RESEND_SUCCESS": "Sự kiện chuyển đổi đã đư<PERSON><PERSON> đưa vào hàng đợi để gửi lại", "RESEND_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại sự kiện chuyển đổi", "BULK_RESEND_SUCCESS": "<PERSON><PERSON><PERSON> sự kiện chuyển đổi đã đư<PERSON><PERSON> đưa vào hàng đợi để gửi lại hàng loạt", "BULK_RESEND_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại hàng loạt các sự kiện chuyển đổi", "EXPORT_SUCCESS": "<PERSON><PERSON> xuất dữ liệu Facebook dataset thành công", "EXPORT_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể xuất dữ liệu Facebook dataset"}, "DATASET_MANAGEMENT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON> lý tập trung cấu hình Facebook/Instagram Dataset cho tất cả các kênh", "CREATE_NEW": "Tạo Dataset mới", "CREATE_FIRST": "<PERSON><PERSON>o Dataset đầu tiên", "EDIT": "Chỉnh sửa", "DELETE_BUTTON": "Xóa", "TEST": "<PERSON><PERSON><PERSON> tra", "ENABLED": "<PERSON><PERSON> bật", "DISABLED": "Đã tắt", "INBOXES_COUNT": "Số Inbox", "EVENT_NAME": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "EVENT_VALUE": "<PERSON><PERSON><PERSON> trị", "LOAD_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách dataset", "DELETE_SUCCESS": "Đã xóa dataset thành công", "DELETE_ERROR": "K<PERSON>ông thể xóa dataset", "TEST_SUCCESS": "<PERSON><PERSON><PERSON> tra kết nối thành công", "TEST_ERROR": "<PERSON><PERSON><PERSON> tra kết nối thất bại", "TOGGLE_SUCCESS": "<PERSON><PERSON> cập nhật trạng thái dataset", "TOGGLE_ERROR": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái dataset", "EMPTY_STATE": {"TITLE": "Chưa có Dataset nào", "DESCRIPTION": "Tạo dataset đầu tiên để bắt đầu theo dõi chuyển đổi Facebook/Instagram"}, "DELETE_MODAL": {"TITLE": "Xóa Dataset", "MESSAGE": "Bạn có chắc chắn muốn xóa dataset '{name}'? Hành động này không thể hoàn tác.", "CONFIRM": "Xóa", "CANCEL": "<PERSON><PERSON><PERSON>"}, "FORM": {"TITLE_CREATE": "Tạo Dataset mới", "TITLE_EDIT": "Chỉnh sửa Dataset", "NAME": "Tên Dataset", "NAME_PLACEHOLDER": "<PERSON><PERSON>ập tên dataset", "PLATFORM": "<PERSON><PERSON><PERSON> t<PERSON>", "PLATFORM_FACEBOOK": "Facebook", "PLATFORM_INSTAGRAM": "Instagram", "PLATFORM_META": "Meta (Facebook + Instagram)", "DESCRIPTION": "<PERSON><PERSON>", "DESCRIPTION_PLACEHOLDER": "Mô tả dataset (t<PERSON><PERSON> chọn)", "SAVE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "SAVE_SUCCESS": "Đ<PERSON> lưu dataset thành công", "SAVE_ERROR": "<PERSON><PERSON><PERSON>ng thể lưu dataset"}}}}