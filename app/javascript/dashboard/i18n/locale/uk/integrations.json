{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "Скасувати", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Інтегр<PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Події з підпискою", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "Скасувати", "DESC": "Вебхуки автоматично повідомляють про те, що відбувається у вашому обліковому записі Chatwoot. Будь ласка, введіть дійсний URL для налаштування зворотного виклику.", "SUBSCRIPTIONS": {"LABEL": "Події", "EVENTS": {"CONVERSATION_CREATED": "Розмову створено", "CONVERSATION_STATUS_CHANGED": "Статус розмови змінено", "CONVERSATION_UPDATED": "Розмову оновлено", "MESSAGE_CREATED": "Повідомлення створено", "MESSAGE_UPDATED": "Повідомлення оновлено", "WEBWIDGET_TRIGGERED": "Віджет онлайн чату відкрий користувачем", "CONTACT_CREATED": "Контакт створено", "CONTACT_UPDATED": "Контакт оновлено", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "URL вебхука", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Будь ласка, введіть правильний URL"}, "EDIT_SUBMIT": "Оновити вебхук", "ADD_SUBMIT": "Створити вебхук"}, "TITLE": "Веб-хук", "CONFIGURE": "Настроїти", "HEADER": "Налаштування вебхука", "HEADER_BTN_TXT": "Додати новий вебхук", "LOADING": "Отримання прикріплених вебхуків", "SEARCH_404": "Немає елементів, що відповідають запиту", "SIDEBAR_TXT": "<p><b>В<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b> </p> <p>Вебхуки - це виклики по HTTP, які можна налаштувати для кожного облікового запису. Вебхук виклики будуть відбуватися при подіях у Chatwoot, наприклад при нових зверненнях. Ви можете створити один чи кілька вебхуків. <br /><br /> Для створення <b>вебхука</b>, натисніть кнопку <b>Додати новий вебхук</b>. Ви тако можете видаляти вебхуки, натиснувши кнопку Видалити.</p>", "LIST": {"404": "Немає налаштованих вебхуків для цього облікового запису.", "TITLE": "Керування вебхуками", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Кінцева точка вебхука", "ACTIONS": "Дії"}}, "EDIT": {"BUTTON_TEXT": "Редагувати", "TITLE": "Редагувати вебхук", "API": {"SUCCESS_MESSAGE": "Налаштування вебхуків успішно оновлено", "ERROR_MESSAGE": "Не вдалося підключитися до Woot Server, спробуйте ще раз пізніше"}}, "ADD": {"CANCEL": "Скасувати", "TITLE": "Додати новий вебхук", "API": {"SUCCESS_MESSAGE": "Налаштування вебхуків успішно додано", "ERROR_MESSAGE": "Не вдалося підключитися до Woot Server, спробуйте ще раз пізніше"}}, "DELETE": {"BUTTON_TEXT": "Видалити", "API": {"SUCCESS_MESSAGE": "Вебхук успішно видалено", "ERROR_MESSAGE": "Не вдалося підключитися до Woot Server, спробуйте ще раз пізніше"}, "CONFIRM": {"TITLE": "Підтвердження видалення", "MESSAGE": "Ви впевнені, що хочете видалити webhook? ({webhookURL})", "YES": "Так, видалити ", "NO": "Ні, залиште"}}}, "SLACK": {"DELETE": "Видалити", "DELETE_CONFIRMATION": {"TITLE": "Видалити інтеграцію", "MESSAGE": "Ви впевнені, що бажаєте видалити інтеграцію? Виконання цього призведе до втрати доступу до розмов на робочому місці Slack."}, "HELP_TEXT": {"TITLE": "Використання Slack інтеграцію", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "вибрано"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Обра<PERSON>и канал", "UPDATE": "Оновити", "BUTTON_TEXT": "Підключити канал", "DESCRIPTION": "Ваш робочий простір Slack повністю пов'язаний з Chatwoot. Але інтеграція в даний час неактивна. Щоб активувати інтеграцію та підключити канал до Chatwoot, натисніть кнопку нижче.\n\n**Примітка:** Якщо ви намагаєтеся підключити приватний канал, додайте Chatwoot додаток до каналу Slack, перш ніж продовжити цей крок.", "ATTENTION_REQUIRED": "Потрібно звернути увагу", "EXPIRED": "Ваша інтеграція зі Slack закінчилася. Щоб продовжити отримувати повідомлення в Slack, будь ласка, видаліть інтеграцію і знову підключіть робочу область."}, "UPDATE_ERROR": "Помилка при оновленні інтеграції, будь ласка, спробуйте ще раз", "UPDATE_SUCCESS": "Канал успішно підключено", "FAILED_TO_FETCH_CHANNELS": "Помилка при підключенні каналів з Slack, будь ласка, спробуйте ще раз"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Натисніть тут, щоб приєднатися", "LEAVE_THE_ROOM": "Залишити дзвінок", "START_VIDEO_CALL_HELP_TEXT": "Почати новий відеодзвінок із клієнтом", "JOIN_ERROR": "Помилка при підключенні до дзвінка. Будь ласка, спробуйте ще раз", "CREATE_ERROR": "Помилка при створенні посилання на зустріч, будь ласка, спробуйте знову"}, "OPEN_AI": {"AI_ASSIST": "ШІ асистент", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Пропозиція для відповіді", "SUMMARIZE": "Підсумувати", "REPHRASE": "Покращити написання", "FIX_SPELLING_GRAMMAR": "Виправити правопис і граматику", "SHORTEN": "Скоротити", "EXPAND": "Розширити", "MAKE_FRIENDLY": "Змінити сигнал повідомлення на дружній", "MAKE_FORMAL": "Використовувати формальний сигнал", "SIMPLIFY": "Спростити"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Вміст чернетки", "GENERATED_TITLE": "Згенерований вміст", "AI_WRITING": "ШІ пише", "BUTTONS": {"APPLY": "Використати цю пропозицію", "CANCEL": "Скасувати"}}, "CTA_MODAL": {"TITLE": "Інтегрувати з OpenAI", "DESC": "Додайте розширені функції AI в панель керування за допомогою моделей GPT OpenAI. Для початку введіть ключ API з вашого облікового запису OpenAI.", "KEY_PLACEHOLDER": "Введіть ваш ключ OpenAI API", "BUTTONS": {"NEED_HELP": "Потрібна допомога?", "DISMISS": "Від<PERSON><PERSON><PERSON>ити", "FINISH": "Завершити налаштування"}, "DISMISS_MESSAGE": "Ви можете налаштувати інтеграцію OpenAI пізніше, коли забажаєте.", "SUCCESS_MESSAGE": "Інтеграція з OpenAI успішно налаштована"}, "TITLE": "Покращити з ШІ", "SUMMARY_TITLE": "Резюме ШІ", "REPLY_TITLE": "Пропозиц<PERSON><PERSON> відповіді від ШІ", "SUBTITLE": "Поліпшена відповідь буде згенерована за допомогою AI, основана на вашій чернетці.", "TONE": {"TITLE": "Тон", "OPTIONS": {"PROFESSIONAL": "Професійний", "FRIENDLY": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "BUTTONS": {"GENERATE": "Згенерувати", "GENERATING": "Генерація...", "CANCEL": "Скасувати"}, "GENERATE_ERROR": "Сталася помилка при обробці контенту, будь ласка, спробуйте ще раз"}, "DELETE": {"BUTTON_TEXT": "Видалити", "API": {"SUCCESS_MESSAGE": "Інтеграція успішно видалена"}}, "CONNECT": {"BUTTON_TEXT": "Підключитися"}, "DASHBOARD_APPS": {"TITLE": "Додатки для головного екрану", "HEADER_BTN_TXT": "Додати нову інформаційну панель", "SIDEBAR_TXT": "<p><b>Панель управління</b></p><p>Додатки дозволяють організаціям вставляти в панель керування Chatwoot інформацію, щоб надати контекст агентам підтримки клієнтів. Ця функція дозволяє створювати додаток незалежно і вставляти його в панелі управління відомості про користувача, їхні замовлення або історію попередніх платежів.</p><p>Коли ви вставляєте свій застосунок за допомогою панелі керування в Chatwoot, ваша програма отримає контекст розмови і зв'яжеться як віконна подія. Налаштуйте слухача для заходу повідомлення на вашій сторінці, щоб отримати контекст.</p><p>Щоб додати нову панель керування, натисніть кнопку 'Додати нову програму'.</p>", "DESCRIPTION": "Додатки дозволяють організаціям вбудовувати інформаційну панель керування і надавати контекст для агентів підтримки клієнтів. Ця функція дозволяє створювати додаток незалежно і додавати інформацію, що надавали про користувача, його замовлення або історію платежів.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Поки що немає налаштованих додатків в цьому акаунті", "LOADING": "Отримання додатків панелі керування...", "TABLE_HEADER": {"NAME": "Ім'я", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Редагувати додаток", "DELETE_TOOLTIP": "Видалити додаток"}, "FORM": {"TITLE_LABEL": "Ім'я", "TITLE_PLACEHOLDER": "Введіть ім'я для вашої інформаційної панелі", "TITLE_ERROR": "Потрібно ім'я для головної панелі", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Введіть URL кінцевої точки, де розміщено ваш додаток", "URL_ERROR": "Потрібно вказати допустиму URL-адресу"}, "CREATE": {"HEADER": "Додати нову інформаційну панель", "FORM_SUBMIT": "Додати", "FORM_CANCEL": "Скасувати", "API_SUCCESS": "Панель інструментів успішно налаштовано", "API_ERROR": "Не вдалося створити додаток. Будь ласка, спробуйте ще раз пізніше"}, "UPDATE": {"HEADER": "Редагувати інформаційну панель", "FORM_SUBMIT": "Оновити", "FORM_CANCEL": "Скасувати", "API_SUCCESS": "Панель інструментів успішно оновлено", "API_ERROR": "Не вдалося оновити додаток. Будь ласка, спробуйте ще раз пізніше"}, "DELETE": {"CONFIRM_YES": "Так, видалити це", "CONFIRM_NO": "Ні, зберегти це", "TITLE": "Підтвердіть видалення", "MESSAGE": "Ви дійсно бажаєте видалити програму - {appName}?", "API_SUCCESS": "Панель інструментів успішно видалено", "API_ERROR": "Не вдалося видалити додаток. Будь ласка, спробуйте ще раз пізніше"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Створити", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Назва", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Необхідно вказати назву"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Команда", "PLACEHOLDER": "Виберіть команду", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Пріоритет", "PLACEHOLDER": "Оберіть пріоритет", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Мітка", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Статус", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Створити", "CANCEL": "Скасувати", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Статус", "PRIORITY": "Пріоритет", "ASSIGNEE": "Assignee", "LABELS": "Мітки", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "NO_LINKED_ISSUES": "No linked issues found", "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Так, видалити", "CANCEL": "Скасувати"}, "CTA": {"TITLE": "Connect to Linear", "AGENT_DESCRIPTION": "Linear workspace is not connected. Request your administrator to connect a workspace to use this integration.", "DESCRIPTION": "Linear workspace is not connected. Click the button below to connect your workspace to use this integration.", "BUTTON_TEXT": "Connect Linear workspace"}}, "NOTION": {"DELETE": {"TITLE": "Are you sure you want to delete the Notion integration?", "MESSAGE": "Deleting this integration will remove access to your Notion workspace and stop all related functionality.", "CONFIRM": "Так, видалити", "CANCEL": "Скасувати"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"TITLE": "Copilot", "TRY_THESE_PROMPTS": "Try these prompts", "PANEL_TITLE": "Get started with Copilot", "KICK_OFF_MESSAGE": "Need a quick summary, want to check past conversations, or draft a better reply? <PERSON><PERSON><PERSON>’s here to speed things up.", "SEND_MESSAGE": "Надіслати...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "Ви", "USE": "Use this", "RESET": "Reset", "SHOW_STEPS": "Show steps", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}, "HIGH_PRIORITY": {"LABEL": "High priority conversations", "CONTENT": "Give me a summary of all high priority open conversations. Include the conversation ID, customer name (if available), last message content, and assigned agent. Group by status if relevant."}, "LIST_CONTACTS": {"LABEL": "List contacts", "CONTENT": "Show me the list of top 10 contacts. Include name, email or phone number (if available), last seen time, tags (if any)."}}}, "PLAYGROUND": {"USER": "Ви", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Введіть Ваше повідомлення...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Оновити зараз", "CANCEL_ANYTIME": "Ви можете змінити або скасувати план у будь-який час"}, "ENTERPRISE_PAYWALL": {"UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Будь ласка, зверніться до адміністратора для оновлення."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "Скасувати", "CREATE": "Створити", "EDIT": "Оновити"}, "ASSISTANTS": {"HEADER": "Assistants", "NO_ASSISTANTS_AVAILABLE": "There are no assistants available in your account.", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Так, видалити", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "Оновити", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Особливості", "TOOLS": "Tools "}, "NAME": {"LABEL": "Ім'я", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "TEMPERATURE": {"LABEL": "Response Temperature", "DESCRIPTION": "Adjust how creative or restrictive the assistant's responses should be. Lower values produce more focused and deterministic responses, while higher values allow for more creative and varied outputs."}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Особливості", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Так, видалити", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Видалити", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Так, видалити", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Всі"}, "STATUS": {"TITLE": "Статус", "PENDING": "Очікує", "APPROVED": "Approved", "ALL": "Всі"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Від'єднатись"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Так, видалити", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Вхідні", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}