{"INBOX_MGMT": {"HEADER": "Inboxes", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "There are no inboxes attached to this account."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "Choose Channel", "BODY": "Choose the provider you want to integrate with Chatwoot."}, "INBOX": {"TITLE": "Create Inbox", "BODY": "Authenticate your account and create an inbox."}, "AGENT": {"TITLE": "Add Agents", "BODY": "Add agents to the created inbox."}, "FINISH": {"TITLE": "Voilà!", "BODY": "You are all set to go!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Enter your inbox name (eg: Acme Inc)", "ERROR": "Please enter a valid inbox name"}, "WEBSITE_NAME": {"LABEL": "Website Name", "PLACEHOLDER": "Enter your website name (eg: Acme Inc)"}, "FB": {"HELP": "PS: By signing in, we only get access to your Page's messages. Your private messages can never be accessed by Chatwoot.", "CHOOSE_PAGE": "<PERSON><PERSON>", "CHOOSE_PLACEHOLDER": "Select a page from the list", "INBOX_NAME": "Inbox Name", "ADD_NAME": "Add a name for your inbox", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "Pick a value", "CREATE_INBOX": "Create Inbox"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continue with Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Connect your Instagram Profile", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "To add your Twitter profile as a channel, you need to authenticate your Twitter Profile by clicking on 'Sign in with Twitter' ", "ERROR_MESSAGE": "There was an error connecting to Twitter, please try again", "TWEETS": {"ENABLE": "Create conversations from mentioned Tweets"}}, "WEBSITE_CHANNEL": {"TITLE": "Website channel", "DESC": "Create a channel for your website and start supporting your customers via our website widget.", "LOADING_MESSAGE": "Creating Website Support Channel", "CHANNEL_AVATAR": {"LABEL": "Channel Avatar"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "Please enter a valid URL"}, "CHANNEL_DOMAIN": {"LABEL": "Website Domain", "PLACEHOLDER": "Enter your website domain (eg: acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "Welcome Heading", "PLACEHOLDER": "Hi there !"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Welcome Tagline", "PLACEHOLDER": "We make it simple to connect with us. Ask us anything, or share your feedback."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Channel greeting message", "PLACEHOLDER": "Acme Inc typically replies in a few hours."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Enable channel greeting", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "Enabled", "DISABLED": "Disabled"}, "REPLY_TIME": {"TITLE": "Set Reply time", "IN_A_FEW_MINUTES": "In a few minutes", "IN_A_FEW_HOURS": "In a few hours", "IN_A_DAY": "In a day", "HELP_TEXT": "This reply time will be displayed on the live chat widget"}, "WIDGET_COLOR": {"LABEL": "Widget Color", "PLACEHOLDER": "Update the widget color used in widget"}, "SUBMIT_BUTTON": "Create inbox", "API": {"ERROR_MESSAGE": "We were not able to create a website channel, please try again"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Channel", "DESC": "Integrate Twilio and start supporting your customers via SMS or WhatsApp.", "ACCOUNT_SID": {"LABEL": "Account SID", "PLACEHOLDER": "Please enter your <PERSON><PERSON><PERSON> Account SID", "ERROR": "This field is required"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "This field is required"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "This field is required"}, "MESSAGING_SERVICE_SID": {"LABEL": "Messaging Service SID", "PLACEHOLDER": "Please enter your Twilio Messaging Service SID", "ERROR": "This field is required", "USE_MESSAGING_SERVICE": "Use a Twilio Messaging Service"}, "CHANNEL_TYPE": {"LABEL": "Channel Type", "ERROR": "Please select your Channel Type"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Please enter your <PERSON><PERSON><PERSON>", "ERROR": "This field is required"}, "CHANNEL_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "This field is required"}, "PHONE_NUMBER": {"LABEL": "فون نمبر", "PLACEHOLDER": "Please enter the phone number from which message will be sent.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Twilio with the URL mentioned here."}, "SUBMIT_BUTTON": "Create Twilio <PERSON>", "API": {"ERROR_MESSAGE": "We were not able to authenticate <PERSON><PERSON><PERSON> credentials, please try again"}}, "SMS": {"TITLE": "SMS Channel", "DESC": "Start supporting your customers via SMS.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "We were not able to save the SMS channel"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Account ID", "PLACEHOLDER": "Please enter your Bandwidth Account ID", "ERROR": "This field is required"}, "API_KEY": {"LABEL": "API Key", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "This field is required"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "This field is required"}, "APPLICATION_ID": {"LABEL": "Application ID", "PLACEHOLDER": "Please enter your Bandwidth Application ID", "ERROR": "This field is required"}, "INBOX_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "This field is required"}, "PHONE_NUMBER": {"LABEL": "فون نمبر", "PLACEHOLDER": "Please enter the phone number from which message will be sent.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "SUBMIT_BUTTON": "Create Bandwidth Channel", "API": {"ERROR_MESSAGE": "We were not able to authenticate Bandwidth credentials, please try again"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Bandwidth with the URL mentioned here."}}}, "WHATSAPP": {"TITLE": "WhatsApp Channel", "DESC": "Start supporting your customers via WhatsApp.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "Inbox Name", "PLACEHOLDER": "Please enter an inbox name", "ERROR": "This field is required"}, "PHONE_NUMBER": {"LABEL": "فون نمبر", "PLACEHOLDER": "Please enter the phone number from which message will be sent.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "PHONE_NUMBER_ID": {"LABEL": "Phone number ID", "PLACEHOLDER": "Please enter the Phone number ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Business Account ID", "PLACEHOLDER": "Please enter the Business Account ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Verify Token", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Please enter a valid value."}, "API_KEY": {"LABEL": "API key", "SUBTITLE": "Configure the WhatsApp API key.", "PLACEHOLDER": "API key", "ERROR": "Please enter a valid value."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL and the verification token in the Facebook Developer portal with the values shown below.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verification Token"}, "SUBMIT_BUTTON": "Create WhatsApp Channel", "API": {"ERROR_MESSAGE": "We were not able to save the WhatsApp channel"}}, "VOICE": {"TITLE": "Voice Channel", "DESC": "Integrate Twilio Voice and start supporting your customers via phone calls.", "PHONE_NUMBER": {"LABEL": "Phone Number", "PLACEHOLDER": "Enter your phone number (e.g. +**********)", "ERROR": "Please provide a valid phone number in E.164 format (e.g. +**********)"}, "TWILIO": {"ACCOUNT_SID": {"LABEL": "Account SID", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON> Account SID", "REQUIRED": "Account SID is required"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON>", "REQUIRED": "Auth Token is required"}, "API_KEY_SID": {"LABEL": "API Key SID", "PLACEHOLDER": "Enter your Twilio API Key SID", "REQUIRED": "API Key SID is required"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Enter your Twilio API Key Secret", "REQUIRED": "API Key Secret is required"}, "TWIML_APP_SID": {"LABEL": "TwiML App SID", "PLACEHOLDER": "Enter your Twilio TwiML App SID (starts with AP)", "REQUIRED": "TwiML App SID is required"}}, "SUBMIT_BUTTON": "Create Voice Channel", "API": {"ERROR_MESSAGE": "We were not able to create the voice channel"}}, "API_CHANNEL": {"TITLE": "API Channel", "DESC": "Integrate with API channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "Channel Name", "PLACEHOLDER": "Please enter a channel name", "ERROR": "This field is required"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Create API Channel", "API": {"ERROR_MESSAGE": "We were not able to save the api channel"}}, "EMAIL_CHANNEL": {"TITLE": "Email Channel", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "Channel Name", "PLACEHOLDER": "Please enter a channel name", "ERROR": "This field is required"}, "EMAIL": {"LABEL": "Email", "SUBTITLE": "Email where your customers sends you support tickets", "PLACEHOLDER": "Email"}, "SUBMIT_BUTTON": "Create Email Channel", "API": {"ERROR_MESSAGE": "We were not able to save the email channel"}, "FINISH_MESSAGE": "Start forwarding your emails to the following email address."}, "LINE_CHANNEL": {"TITLE": "LINE Channel", "DESC": "Integrate with LINE channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "Channel Name", "PLACEHOLDER": "Please enter a channel name", "ERROR": "This field is required"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Channel ID", "PLACEHOLDER": "LINE Channel ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Channel Token", "PLACEHOLDER": "LINE Channel Token"}, "SUBMIT_BUTTON": "Create LINE Channel", "API": {"ERROR_MESSAGE": "We were not able to save the LINE channel"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL in LINE application with the URL mentioned here."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Channel", "DESC": "Integrate with Telegram channel and start supporting your customers.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure the bot token you have obtained from Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Create Telegram Channel", "API": {"ERROR_MESSAGE": "We were not able to save the telegram channel"}}, "AUTH": {"TITLE": "Choose a channel", "DESC": "Chatwoot supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "ایجنٹ", "DESC": "Here you can add agents to manage your newly created inbox. Only these selected agents will have access to your inbox. Agents which are not part of this inbox will not be able to see or respond to messages in this inbox when they login. <br> <b>PS:</b> As an administrator, if you need access to all inboxes, you should add yourself as agent to all inboxes that you create.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Pick agents for the inbox"}, "DETAILS": {"TITLE": "Inbox Details", "DESC": "From the dropdown below, select the Facebook Page you want to connect to Chatwoot. You can also give a custom name to your inbox for better identification."}, "FINISH": {"TITLE": "Nailed It!", "DESC": "You have successfully finished integrating your Facebook Page with Chatwoot. Next time a customer messages your Page, the conversation will automatically appear on your inbox.<br>We are also providing you with a widget script that you can easily add to your website. Once this is live on your website, customers can message you right from your website without the help of any external tool and the conversation will appear right here, on Chatwoot.<br>Cool, huh? Well, we sure try to be :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "Authenticating you with Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "Something went wrong, Please refresh page...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "Creating your Inbox...", "TITLE": "Configure Inbox Details", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Add agents", "ADD_AGENTS": "Adding Agents to your Inbox..."}, "FINISH": {"TITLE": "Your Inbox is ready!", "MESSAGE": "You can now engage with your customers through your new Channel. Happy supporting", "BUTTON_TEXT": "Take me there", "MORE_SETTINGS": "More settings", "WEBSITE_SUCCESS": "You have successfully finished creating a website channel. Copy the code shown below and paste it on your website. Next time a customer use the live chat, the conversation will automatically appear on your inbox."}, "REAUTH": "Reauthorize", "VIEW": "View", "EDIT": {"API": {"SUCCESS_MESSAGE": "Inbox settings updated successfully", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Auto assignment updated successfully", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "ENABLE_CSAT": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Friendly", "FROM": "from", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Save"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "ENABLE_HMAC": {"LABEL": "Enable"}}, "DELETE": {"BUTTON_TEXT": "حذف کریں۔", "AVATAR_DELETE_BUTTON_TEXT": "Delete Avatar", "CONFIRM": {"TITLE": "حذف کرنے کی تصدیق کریں۔", "MESSAGE": "کیا آپ کو یقینی طور پر حذف کرنا ہے۔ ", "PLACE_HOLDER": "Please type {inboxName} to confirm", "YES": "ہاں، حذف کریں۔ ", "NO": "نہیں ، رہنے دیں "}, "API": {"SUCCESS_MESSAGE": "Inbox deleted successfully", "ERROR_MESSAGE": "Could not delete inbox. Please try again later.", "AVATAR_SUCCESS_MESSAGE": "Inbox avatar deleted successfully", "AVATAR_ERROR_MESSAGE": "Could not delete the inbox avatar. Please try again later."}}, "TABS": {"SETTINGS": "Settings", "COLLABORATORS": "Collaborators", "CONFIGURATION": "Configuration", "CAMPAIGN": "Campaigns", "PRE_CHAT_FORM": "Pre Chat Form", "BUSINESS_HOURS": "Business Hours", "WIDGET_BUILDER": "Widget Builder", "BOT_CONFIGURATION": "Bot Configuration", "CSAT": "CSAT"}, "SETTINGS": "Settings", "FEATURES": {"LABEL": "Features", "DISPLAY_FILE_PICKER": "Display file picker on the widget", "DISPLAY_EMOJI_PICKER": "Display emoji picker on the widget", "ALLOW_END_CONVERSATION": "Allow users to end conversation from the widget", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON>", "MESSENGER_SUB_HEAD": "Place this button inside your body tag", "INBOX_AGENTS": "ایجنٹ", "INBOX_AGENTS_SUB_TEXT": "Add or remove agents from this inbox", "AGENT_ASSIGNMENT": "Conversation Assignment", "AGENT_ASSIGNMENT_SUB_TEXT": "Update conversation assignment settings", "UPDATE": "Update", "ENABLE_EMAIL_COLLECT_BOX": "Enable email collect box", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Enable or disable email collect box on new conversation", "AUTO_ASSIGNMENT": "Enable auto assignment", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "Enable conversation continuity via email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "Conversations will continue over email if the contact email address is available.", "LOCK_TO_SINGLE_CONVERSATION": "Lock to single conversation", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Enable or disable multiple conversations for the same contact in this inbox", "INBOX_UPDATE_TITLE": "Inbox Settings", "INBOX_UPDATE_SUB_TEXT": "Update your inbox settings", "AUTO_ASSIGNMENT_SUB_TEXT": "Enable or disable the automatic assignment of new conversations to the agents added to this inbox.", "HMAC_VERIFICATION": "User Identity Validation", "HMAC_DESCRIPTION": "With this key you can generate a secret token that can be used to verify the identity of your users.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "Enforce User Identity Validation", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests that cannot be verified will be rejected.", "INBOX_IDENTIFIER": "Inbox Identifier", "INBOX_IDENTIFIER_SUB_TEXT": "Use the `inbox_identifier` token shown here to authentication your API clients.", "FORWARD_EMAIL_TITLE": "Forward to Email", "FORWARD_EMAIL_SUB_TEXT": "Start forwarding your emails to the following email address.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Allow messages after conversation resolved", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Allow the end-users to send messages even after the conversation is resolved.", "WHATSAPP_SECTION_SUBHEADER": "This API Key is used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API Key", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "Update", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verification Token", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Auto assignment limit", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Please enter a value greater than 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limit the maximum number of conversations from this inbox that can be auto assigned to an agent"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Reauthorize", "SUBTITLE": "Your Facebook connection has expired, please reconnect your Facebook page to continue services", "MESSAGE_SUCCESS": "Reconnection successful", "MESSAGE_ERROR": "There was an error, please try again"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pre chat forms enable you to capture user information before they start conversation with you.", "SET_FIELDS": "Pre chat form fields", "SET_FIELDS_HEADER": {"FIELDS": "Fields", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "Key", "TYPE": "Type", "REQUIRED": "Required"}, "ENABLE": {"LABEL": "Enable pre chat form", "OPTIONS": {"ENABLED": "Yes", "DISABLED": "No"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pre chat message", "PLACEHOLDER": "This message would be visible to the users along with the form"}, "REQUIRE_EMAIL": {"LABEL": "Visitors should provide their name and email address before starting the chat"}}, "CSAT": {"TITLE": "Enable CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "Message", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "contains", "DOES_NOT_CONTAINS": "does not contain"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Set your availability", "SUBTITLE": "Set your availability on your livechat widget", "WEEKLY_TITLE": "Set your weekly hours", "TIMEZONE_LABEL": "Select timezone", "UPDATE": "Update business hours settings", "TOGGLE_AVAILABILITY": "Enable business availability for this inbox", "UNAVAILABLE_MESSAGE_LABEL": "Unavailable message for visitors", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Enable availability for this day", "UNAVAILABLE": "Unavailable", "HOURS": "hours", "VALIDATION_ERROR": "Starting time should be before closing time.", "CHOOSE": "<PERSON><PERSON>"}, "ALL_DAY": "All-Day"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Set your IMAP details", "NOTE_TEXT": "To enable SMTP, please configure IMAP.", "UPDATE": "Update IMAP settings", "TOGGLE_AVAILABILITY": "Enable IMAP configuration for this inbox", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP settings updated successfully", "ERROR_MESSAGE": "Unable to update IMAP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "PASSWORD": {"LABEL": "Password", "PLACE_HOLDER": "Password"}, "ENABLE_SSL": "Enable SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Set your SMTP details", "UPDATE": "Update SMTP settings", "TOGGLE_AVAILABILITY": "Enable SMTP configuration for this inbox", "TOGGLE_HELP": "Enabling SMTP will help the user to send email", "EDIT": {"SUCCESS_MESSAGE": "SMTP settings updated successfully", "ERROR_MESSAGE": "Unable to update SMTP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "PASSWORD": {"LABEL": "Password", "PLACE_HOLDER": "Password"}, "DOMAIN": {"LABEL": "Domain", "PLACE_HOLDER": "Domain"}, "ENCRYPTION": "Encryption", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Mode", "AUTH_MECHANISM": "Authentication"}, "NOTE": "Note: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar deleted successfully", "ERROR_MESSAGE": "ایک خامی تھی، براہ کرم دوبارہ کوشش کریں۔"}}}, "WEBSITE_NAME": {"LABEL": "Website Name", "PLACE_HOLDER": "Enter your website name (eg: Acme Inc)", "ERROR": "Please enter a valid website name"}, "WELCOME_HEADING": {"LABEL": "Welcome Heading", "PLACE_HOLDER": "Hi there!"}, "WELCOME_TAGLINE": {"LABEL": "Welcome Tagline", "PLACE_HOLDER": "We make it simple to connect with us. Ask us anything, or share your feedback."}, "REPLY_TIME": {"LABEL": "Reply Time", "IN_A_FEW_MINUTES": "In a few minutes", "IN_A_FEW_HOURS": "In a few hours", "IN_A_DAY": "In a day"}, "WIDGET_COLOR_LABEL": "Widget Color", "WIDGET_BUBBLE_POSITION_LABEL": "Widget Bubble Position", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Type", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "Chat with us", "LABEL": "Widget Bubble Launcher Title", "PLACE_HOLDER": "Chat with us"}, "UPDATE": {"BUTTON_TEXT": "Update Widget Settings", "API": {"SUCCESS_MESSAGE": "Widget settings updated successfully", "ERROR_MESSAGE": "Unable to update widget settings"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Preview", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Left", "RIGHT": "Right"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standard", "EXPANDED_BUBBLE": "Expanded Bubble"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Typically replies in a few minutes", "IN_A_FEW_HOURS": "Typically replies in a few hours", "IN_A_DAY": "Typically replies in a day"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "Start Conversation", "CHAT_INPUT_PLACEHOLDER": "Type your message"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "We are Online", "OFFLINE": "We are away at the moment"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hello"}, "BRANDING_TEXT": "Powered by <PERSON><PERSON><PERSON><PERSON>", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "ٹویٹر", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "Email", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Channel", "INSTAGRAM": "Instagram", "VOICE": "Voice"}}}