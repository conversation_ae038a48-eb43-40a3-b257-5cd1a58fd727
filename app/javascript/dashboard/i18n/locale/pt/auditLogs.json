{"AUDIT_LOGS": {"HEADER": "Logs de auditoria", "HEADER_BTN_TXT": "Adicionar logs de auditoria", "LOADING": "A obter logs de auditoria", "DESCRIPTION": "Os logs de auditoria mantêm um registo das atividades na sua conta, permitindo-lhe acompanhar e auditar a sua conta, equipa ou serviços.", "LEARN_MORE": "Saber mais sobre os logs de auditoria", "SEARCH_404": "Não existem itens correspondentes a esta consulta", "SIDEBAR_TXT": "<p><b>Logs de auditoria</b> </p><p> Logs de auditoria são registos de eventos e ações de um Sistema de Chatwoot. </p>", "LIST": {"404": "Não há logs de auditoria referentes a esta conta.", "TITLE": "Gerir logs de auditoria", "DESC": "Logs de auditoria são registos de eventos e ações do Sistema Chatwoot.", "TABLE_HEADER": {"ACTIVITY": "User", "TIME": "<PERSON><PERSON><PERSON><PERSON>", "IP_ADDRESS": "Endereço IP"}}, "API": {"SUCCESS_MESSAGE": "Logs de auditoria recuperados com sucesso", "ERROR_MESSAGE": "Não foi possível conectar ao servidor Woot, por favor, tente novamente mais tarde"}, "DEFAULT_USER": "Sistema", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} convidou {invitee} para a conta como {role}", "EDIT": {"SELF": "{agentName} alterou o seu {attributes} para {values}", "OTHER": "{agentName} alterou o parâmetro {attributes} do utilizador {user} para {values}", "DELETED": "{agentName} alterou o parâmetro {attributes} de um utilizador excluído para {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON>ame} in<PERSON><PERSON> sessão", "SIGN_OUT": "{agentName} terminou sessão"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}, "CONVERSATION": {"DELETE": "{agentName} apagou a conversa #{id}"}}}