#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
fa:
  hello: 'سلام دنیا'
  messages:
    reset_password_success: سوت! درخواست ریست شدن رمز عبور با موفقیت ارسال شد. ایمیل خود را چک کنید
    reset_password_failure: اوه نه! کاربری با چنین ایمیلی وجود ندارد
    inbox_deletetion_response: درخواست حذف صندوق ورودی شما پس از مدتی پردازش خواهد شد.
  errors:
    validations:
      presence: نباید خالی باشد
    webhook:
      invalid: رویدادهای نامعتبر
    signup:
      disposable_email: استفاده از ایمیل‌های موقت امکان‌پذیر نیست
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: ایمیل وارد شده معتبر نیست
      email_already_exists: 'قبلا کاربری با ایمیل %{email} ثبت‌نام کرده است'
      invalid_params: 'نامعتبر است، لطفا پارامترهای ثبت‌نام را بررسی کرده و دوباره امتحان کنید'
      failed: ثبت نام ناموفق بود
    data_import:
      data_type:
        invalid: نوع داده نامعتبر است
    contacts:
      import:
        failed: پرونده خالی است
      export:
        success: هر زمان فایل خروجی مخاطبین برای مشاهده آماده شود به شما اطلاع خواهیم داد.
      email:
        invalid: ایمیل نامعتبر است
      phone_number:
        invalid: باید در قالب e164 باشد
    categories:
      locale:
        unique: باید منحصر به فرد در دسته‌بندی و پورتال باشد
    dyte:
      invalid_message_type: 'نوع پیام نامعتبر است. اقدام مجاز نیست'
    slack:
      invalid_channel_id: 'کانال اسلک نامعتبر است. لطفا دوباره تلاش کنید'
    inboxes:
      imap:
        socket_error: لطفا اتصال شبکه، آدرس IMAP را بررسی کنید و دوباره امتحان کنید.
        no_response_error: لطفا اعتبارنامه IMAP را بررسی کنید و دوباره امتحان کنید.
        host_unreachable_error: میزبان غیرقابل دسترسی است، لطفا آدرس IMAP و پورت IMAP را بررسی کنید و دوباره امتحان کنید.
        connection_timed_out_error: زمان اتصال برای %{address}:%{port} تمام شد
        connection_closed_error: اتصال بسته شد.
      validations:
        name: نباید با نمادها شروع یا ختم شود و نباید دارای کاراکترهای < > / \ @ باشد.
    custom_filters:
      number_of_records: سررسید محدودیت. حداکثر تعداد قابل قبول فیلترها برای یک کاربر در هر اکانت 50 می باشند.
      invalid_attribute: کلید ویژگی معتبر نیست (%{key}). کلید باید یکی از %{allowed_keys} باشد یا یک ویژگی سفارشی ایجاد شده در حساب.
      invalid_operator: این عملیات مجاز نیست. عملیات های مجاز برای %{attribute_name} شامل %{allowed_keys} می باشد.
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: مقدار معتبر نیست. مقادیر ارائه شده برای %{attribute_name} معتبر نیست
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: زمان گزارش از %{since} تا %{until}
    utc_warning: گزارش تولید شده در منطقه زمانی UTC است
    agent_csv:
      agent_name: اسم ایجنت
      conversations_count: گفتگو‌های اختصاص داده شده
      avg_first_response_time: میانگین زمان تا اولین پاسخ
      avg_resolution_time: میانگین زمان حل مشکل
      resolution_count: تعداد مسائل حل شده
      avg_customer_waiting_time: میانگین زمان انتظار مشتری
    inbox_csv:
      inbox_name: نام صندوق ورودی
      inbox_type: نوع صندوق ورودی
      conversations_count: تعداد گفتگوها
      avg_first_response_time: میانگین زمان تا اولین پاسخ
      avg_resolution_time: میانگین زمان حل مشکل
    label_csv:
      label_title: برچسب
      conversations_count: تعداد گفتگوها
      avg_first_response_time: میانگین زمان تا اولین پاسخ
      avg_resolution_time: میانگین زمان حل مشکل
      avg_reply_time: Avg reply time
      resolution_count: تعداد مسائل حل شده
    team_csv:
      team_name: نام تیم
      conversations_count: Conversations count
      avg_first_response_time: میانگین زمان تا اولین پاسخ
      avg_resolution_time: میانگین زمان حل مشکل
      resolution_count: تعداد مسائل حل شده
      avg_customer_waiting_time: میانگین زمان انتظار مشتری
    conversation_traffic_csv:
      timezone: منطقه زمانی
    sla_csv:
      conversation_id: شناسه گفتگو
      sla_policy_breached: سیاست SLA
      assignee: مسئول
      team: تیم‌
      inbox: صندوق ورودی
      labels: برچسب‌ها
      conversation_link: پیوند به گفتگو
      breached_events: رویدادهای نقض شده
    default_group_by: روز
    csat:
      headers:
        contact_name: نام تماس
        contact_email_address: آدرس ایمیل تماس
        contact_phone_number: شماره تلفن تماس
        link_to_the_conversation: پیوند به گفتگو
        agent_name: اسم ایجنت
        rating: رتبه
        feedback: بازخورد نظر
        recorded_at: تاریخ ثبت شده
  notifications:
    notification_title:
      conversation_creation: 'یک گفتگو جدید (%{display_id}) در %{inbox_name} ایجاد شده است'
      conversation_assignment: 'یک گفتگو جدید (%{display_id}) به شما اختصاص داده شده'
      assigned_conversation_new_message: 'یک پیام جدید در گفتگوی %{display_id} ارسال شده'
      conversation_mention: 'به نام شما در گفتگو (%{display_id}) اشاره شده است'
      sla_missed_first_response: 'سیاست SLA مربوط به اولین پاسخ در گفتگوی (%{display_id}) نقض شده'
      sla_missed_next_response: 'سیاست SLA مربوط به پاسخ بعدی در گفتگوی (%{display_id}) نقض شده'
      sla_missed_resolution: 'سیاست SLA مربوط به زمان حل موضوع در گفتگوی (%{display_id}) نقض شده'
    attachment: 'پیوست'
    no_content: 'فاقد محتوا'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} در داستان به شما اشاره کرده: '
      instagram_deleted_story_content: این داستان دیگر در دسترس نیست.
      deleted: این پیام حذف شد
      whatsapp:
        list_button_label: 'Choose an item'
      delivery_status:
        error_code: 'کد خطا " %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'مکالمه توسط ایجنت %{user_name} حل شده، اعلام شده بود'
        contact_resolved: 'گفتگو توسط %{contact_name} حل شد'
        open: 'گفتگو توسط ایجنت %{user_name} مجددا باز شده بود'
        pending: 'مکالمه توسط %{user_name} به عنوان معلق علامت گذاری شد'
        snoozed: 'مکالمه توسط %{user_name} به تعویق افتاد'
        auto_resolved_days: 'به دلیل %{count} روز عدم فعالیت ، مکالمه توسط سیستم بسته شد'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: سیستم به دلیل یک پیام دریافتی جدید، مکالمه را دوباره باز کرد.
      priority:
        added: '%{user_name} اولویت را روی %{new_priority} تنظیم کرد'
        updated: '%{user_name} اولویت را از %{old_priority} به %{new_priority} تغییر داد'
        removed: '%{user_name} اولویت را حذف کرد'
      assignee:
        self_assigned: '%{user_name} این مکالمه را به خود اختصاص داد'
        assigned: '%{user_name} گفتگو را به %{assignee_name} اختصاص داد'
        removed: 'گفتگو توسط اپراتور %{user_name} به وضعیت اختصاص داده نشده تغییر یافت'
      team:
        assigned: '%{user_name} گفتگو را به %{team_name} اختصاص داد'
        assigned_with_assignee: 'از طرف %{user_name} به تیم %{team_name} و ایجنت %{assignee_name} اختصاص داده شده است'
        removed: 'اختصاص داده نشده از %{team_name} توسط %{user_name}'
      labels:
        added: '%{user_name}، %{labels} را اضافه کرد'
        removed: '%{user_name}، %{labels} را حذف کرد'
      sla:
        added: '%{user_name} سیاست SLA %{sla_name} را اضافه کرد'
        removed: '%{user_name} سیاست SLA %{sla_name} را حذف کرد'
      linear:
        issue_created: 'Linear issue %{issue_id} was created by %{user_name}'
        issue_linked: 'Linear issue %{issue_id} was linked by %{user_name}'
        issue_unlinked: 'Linear issue %{issue_id} was unlinked by %{user_name}'
      csat:
        not_sent_due_to_messaging_window: 'CSAT survey not sent due to outgoing message restrictions'
      auto_resolve:
        not_sent_due_to_messaging_window: 'Auto-resolve message not sent due to outgoing message restrictions'
      muted: '%{user_name} مکالمه را بی صدا کرد'
      unmuted: '%{user_name} مکالمه را از حالت بی صدا خراج کرد'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} معمولا ظرف مدت کوتاهی پاسخ می‌دهد.'
      ways_to_reach_you_message_body: 'راهی برای ارتباط گرفتن تیم با شما قرار دهید'
      email_input_box_message_body: 'پیام جدیدی به این گفتگو اضافه شده است'
      csat_input_message_body: 'لطفاً به مکالمه امتیاز دهید'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} از %{inbox_name} «%{from_email}»'
          reply_with_name: '%{assignee_name} از %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} از %{business_name} «%{from_email}»'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} از %{inbox_name} «%{from_email}»'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'پیام‌های جدید در این مکالمه'
      transcript_subject: 'متن مکالمه'
    survey:
      response: 'لطفاً به مکالمه امتیاز دهید %{link}'
  contacts:
    online:
      delete: '%{contact_name} آنلاین است ، لطفاً بعداً دوباره امتحان کنید'
  integration_apps:
    #Note: webhooks and dashboard_apps don't need short_description as they use different modal components
    dashboard_apps:
      name: 'برنامه‌های پیشخوان'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      short_description: 'Start video/voice calls with customers directly from Chatwoot.'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} جلسه ای را آغاز کرده است'
    slack:
      name: 'Slack'
      short_description: 'Receive notifications and respond to conversations directly in Slack.'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'وب هوک'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      short_description: 'Build chatbots to handle initial queries before transferring to agents.'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'مترجم گوگل'
      short_description: 'Automatically translate customer messages for agents.'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      short_description: 'AI-powered reply suggestions, summarization, and message enhancement.'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      short_description: 'Create and link Linear issues directly from conversations.'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    notion:
      name: 'Notion'
      short_description: 'Integrate databases, documents and pages directly with Captain.'
      description: 'Connect your Notion workspace to enable Captain to access and generate intelligent responses using content from your databases, documents, and pages to provide more contextual customer support.'
    shopify:
      name: 'Shopify'
      short_description: 'Access order details and customer data from your Shopify store.'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_message_required: پیام الزامی است
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
    copilot:
      using_tool: 'Using tool %{function_name}'
      completed_tool_call: 'Completed %{function_name} tool call'
      invalid_tool_call: 'Invalid tool call'
      tool_not_available: 'Tool not available'
  public_portal:
    search:
      search_placeholder: جستجوی مقاله براساس عنوان یا متن...
      empty_placeholder: نتیجه‌ای یافت نشد.
      loading_placeholder: در حال جستجو...
      results_title: نتایج جستجو
    toc_header: 'در این صفحه'
    hero:
      sub_title: مقالات را در اینجا جستجو کنید یا دسته‌بندی‌های زیر را مرور کنید.
    common:
      home: صفحه اصلی
      last_updated_on: آخرین به‌روزرسانی در تاریخ %{last_updated_on}
      view_all_articles: مشاهده همه
      article: مقاله
      articles: مقالات
      author: نویسنده
      authors: نویسنده ها
      other: دیگر
      others: دیگران
      by: توسط
      no_articles: هنوز هیچ مقاله‌ای در اینجا وجود ندارد
    footer:
      made_with: ساخته شده با
    header:
      go_to_homepage: وب سایت
      visit_website: Visit website
      appearance:
        system: سیستم
        light: روشن
        dark: تیره
      featured_articles: مقالات برگزیده
      uncategorized: دسته بندی نشده
    404:
      title: صفحه یافت نشد
      description: ما نتوانستیم صفحه مورد نظر شما را پیدا کنیم.
      back_to_home: به صفحه اصلی بروید
  slack_unfurl:
    fields:
      name: نام
      email: ایمیل
      phone_number: موبایل
      company_name: شرکت
      inbox_name: صندوق ورودی
      inbox_type: نوع صندوق ورودی
    button: باز کردن گفتگو
  time_units:
    days:
      one: '%{count} روز'
      other: '%{count} روز'
    hours:
      one: '%{count} ساعت'
      other: '%{count} ساعت'
    minutes:
      one: '%{count} قیقه'
      other: '%{count} قیقه'
    seconds:
      one: '%{count} ثانیه'
      other: '%{count} ثانیه'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[فاقد محتوا]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
